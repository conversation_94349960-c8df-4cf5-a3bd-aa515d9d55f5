ALTER TABLE idp DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE setting DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts, DROP COLUMN IF EXISTS description;
ALTER TABLE role DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE environment DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE policy DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE policy ADD COLUMN IF NOT EXISTS updated_ts TIMESTAMPTZ NOT NULL DEFAULT now();
ALTER TABLE project DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE project_webhook DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE instance DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
DROP TABLE IF EXISTS instance_user;
ALTER TABLE data_source DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE task_dag DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updated_ts, DROP COLUMN IF EXISTS payload;
DROP TABLE IF EXISTS activity;
ALTER TABLE issue_comment DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS updater_id;
ALTER TABLE query_history DROP COLUMN IF EXISTS row_status;
ALTER TABLE vcs DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE vcs_connector DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE anomaly DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id;
ALTER TABLE deployment_config DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE external_approval DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE risk DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE slow_query DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE db_group DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE changelist DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id;
DROP TABLE IF EXISTS sql_lint_config;
ALTER TABLE user_group DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE review_config DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE sync_history DROP COLUMN IF EXISTS creator_id;
DROP INDEX IF EXISTS idx_external_approval_row_status_issue_id;
CREATE INDEX idx_external_approval_issue_id ON external_approval(issue_id);