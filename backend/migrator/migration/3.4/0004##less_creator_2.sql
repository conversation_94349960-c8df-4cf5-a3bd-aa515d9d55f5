ALTER TABLE principal DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE db DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE db_schema DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE sheet DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
DROP INDEX IF EXISTS idx_sheet_creator_id;
DROP INDEX IF EXISTS idx_sheet_name;
DROP INDEX IF EXISTS idx_sheet_project_id_row_status;
ALTER TABLE pipeline DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE stage DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE task DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS created_ts, DROP COLUMN IF EXISTS updater_id, DROP COLUMN IF EXISTS updated_ts;
ALTER TABLE task_run DROP COLUMN IF EXISTS updater_id;
ALTER TABLE plan DROP COLUMN IF EXISTS updater_id;
ALTER TABLE plan_check_run DROP COLUMN IF EXISTS creator_id, DROP COLUMN IF EXISTS updater_id;
ALTER TABLE issue DROP COLUMN IF EXISTS updater_id;
ALTER TABLE worksheet DROP COLUMN IF EXISTS row_status, DROP COLUMN IF EXISTS updater_id;
ALTER TABLE revision DROP COLUMN IF EXISTS creator_id;
ALTER TABLE changelog DROP COLUMN IF EXISTS creator_id;