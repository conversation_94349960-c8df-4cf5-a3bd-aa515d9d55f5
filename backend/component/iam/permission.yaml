permissions:
  - bb.auditLogs.export
  - bb.auditLogs.search
  - bb.changelists.create
  - bb.changelists.delete
  - bb.changelists.get
  - bb.changelists.list
  - bb.changelists.update
  - bb.changelogs.get
  - bb.changelogs.list
  - bb.databaseCatalogs.get
  - bb.databaseCatalogs.update
  - bb.databaseSecrets.delete
  - bb.databaseSecrets.list
  - bb.databaseSecrets.update
  - bb.databases.adviseIndex
  - bb.databases.check
  - bb.databases.get
  - bb.databases.getSchema
  - bb.databases.list
  - bb.databases.sync
  - bb.databases.update
  - bb.groups.create
  - bb.groups.delete
  - bb.groups.get
  - bb.groups.list
  - bb.groups.update
  - bb.identityProviders.create
  - bb.identityProviders.delete
  - bb.identityProviders.get
  - bb.identityProviders.undelete
  - bb.identityProviders.update
  - bb.instances.create
  - bb.instances.delete
  - bb.instances.get
  - bb.instances.list
  - bb.instances.sync
  - bb.instances.undelete
  - bb.instances.update
  - bb.instanceRoles.get
  - bb.instanceRoles.list
  - bb.issueComments.create
  - bb.issueComments.list
  - bb.issueComments.update
  - bb.issues.create
  - bb.issues.get
  - bb.issues.list
  - bb.issues.update
  - bb.planCheckRuns.list
  - bb.planCheckRuns.run
  - bb.plans.create
  - bb.plans.get
  - bb.plans.list
  - bb.plans.preview
  - bb.plans.update
  - bb.policies.create
  - bb.policies.delete
  - bb.policies.get
  - bb.policies.list
  - bb.policies.update
  - bb.projects.create
  - bb.projects.delete
  - bb.projects.get
  - bb.projects.getIamPolicy
  - bb.projects.list
  - bb.projects.setIamPolicy
  - bb.projects.undelete
  - bb.projects.update
  - bb.releases.check
  - bb.releases.create
  - bb.releases.delete
  - bb.releases.get
  - bb.releases.list
  - bb.releases.undelete
  - bb.releases.update
  - bb.reviewConfigs.create
  - bb.reviewConfigs.delete
  - bb.reviewConfigs.get
  - bb.reviewConfigs.list
  - bb.reviewConfigs.update
  - bb.revisions.create
  - bb.revisions.delete
  - bb.revisions.get
  - bb.revisions.list
  - bb.risks.create
  - bb.risks.delete
  - bb.risks.list
  - bb.risks.update
  - bb.roles.create
  - bb.roles.delete
  - bb.roles.list
  - bb.roles.get
  - bb.roles.update
  - bb.rollouts.create
  - bb.rollouts.get
  - bb.rollouts.list
  - bb.rollouts.preview
  - bb.settings.get
  - bb.settings.list
  - bb.settings.set
  - bb.sheets.create
  - bb.sheets.get
  - bb.sheets.update
  - bb.slowQueries.list
  - bb.sql.select
  - bb.sql.ddl
  - bb.sql.dml
  - bb.sql.explain
  - bb.sql.info
  - bb.sql.export
  - bb.sql.admin
  - bb.taskRuns.create
  - bb.taskRuns.list
  - bb.users.create
  - bb.users.delete
  - bb.users.undelete
  - bb.users.update
  - bb.worksheets.get
  - bb.worksheets.manage
  - bb.workspaces.getIamPolicy
  - bb.workspaces.setIamPolicy
