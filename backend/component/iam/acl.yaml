roles:
  - name: roles/workspaceAdmin
    title: Workspace admin
    permissions:
      - bb.auditLogs.export
      - bb.auditLogs.search
      - bb.changelists.create
      - bb.changelists.delete
      - bb.changelists.get
      - bb.changelists.list
      - bb.changelists.update
      - bb.changelogs.get
      - bb.changelogs.list
      - bb.databaseCatalogs.get
      - bb.databaseCatalogs.update
      - bb.databaseSecrets.delete
      - bb.databaseSecrets.list
      - bb.databaseSecrets.update
      - bb.databases.adviseIndex
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.databases.sync
      - bb.databases.update
      - bb.identityProviders.create
      - bb.identityProviders.delete
      - bb.identityProviders.get
      - bb.identityProviders.undelete
      - bb.identityProviders.update
      - bb.instances.create
      - bb.instances.delete
      - bb.instances.get
      - bb.instances.list
      - bb.instances.sync
      - bb.instances.undelete
      - bb.instances.update
      - bb.instanceRoles.get
      - bb.instanceRoles.list
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.create
      - bb.issues.get
      - bb.issues.list
      - bb.issues.update
      - bb.planCheckRuns.list
      - bb.planCheckRuns.run
      - bb.plans.create
      - bb.plans.get
      - bb.plans.list
      - bb.plans.preview
      - bb.plans.update
      - bb.policies.create
      - bb.policies.delete
      - bb.policies.get
      - bb.policies.list
      - bb.policies.update
      - bb.projects.create
      - bb.projects.delete
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.projects.list
      - bb.projects.setIamPolicy
      - bb.projects.undelete
      - bb.projects.update
      - bb.releases.check
      - bb.releases.create
      - bb.releases.delete
      - bb.releases.get
      - bb.releases.list
      - bb.releases.undelete
      - bb.releases.update
      - bb.reviewConfigs.create
      - bb.reviewConfigs.delete
      - bb.reviewConfigs.get
      - bb.reviewConfigs.list
      - bb.reviewConfigs.update
      - bb.revisions.create
      - bb.revisions.delete
      - bb.revisions.get
      - bb.revisions.list
      - bb.risks.create
      - bb.risks.delete
      - bb.risks.list
      - bb.risks.update
      - bb.roles.create
      - bb.roles.delete
      - bb.roles.list
      - bb.roles.get
      - bb.roles.update
      - bb.rollouts.create
      - bb.rollouts.get
      - bb.rollouts.list
      - bb.rollouts.preview
      - bb.settings.get
      - bb.settings.list
      - bb.settings.set
      - bb.sheets.create
      - bb.sheets.get
      - bb.sheets.update
      - bb.slowQueries.list
      - bb.taskRuns.create
      - bb.taskRuns.list
      - bb.groups.create
      - bb.groups.delete
      - bb.groups.get
      - bb.groups.list
      - bb.groups.update
      - bb.sql.admin
      - bb.sql.ddl
      - bb.sql.dml
      - bb.sql.explain
      - bb.sql.info
      - bb.sql.select
      - bb.sql.export
      - bb.users.create
      - bb.users.delete
      - bb.users.undelete
      - bb.users.update
      - bb.worksheets.get
      - bb.worksheets.manage
      - bb.workspaces.getIamPolicy
      - bb.workspaces.setIamPolicy
  - name: roles/workspaceDBA
    title: Workspace DBA
    permissions:
      - bb.auditLogs.export
      - bb.auditLogs.search
      - bb.changelists.create
      - bb.changelists.delete
      - bb.changelists.get
      - bb.changelists.list
      - bb.changelists.update
      - bb.changelogs.get
      - bb.changelogs.list
      - bb.databaseCatalogs.get
      - bb.databaseCatalogs.update
      - bb.databaseSecrets.delete
      - bb.databaseSecrets.list
      - bb.databaseSecrets.update
      - bb.databases.adviseIndex
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.databases.sync
      - bb.databases.update
      - bb.identityProviders.create
      - bb.identityProviders.delete
      - bb.identityProviders.get
      - bb.identityProviders.undelete
      - bb.identityProviders.update
      - bb.instances.create
      - bb.instances.delete
      - bb.instances.get
      - bb.instances.list
      - bb.instances.sync
      - bb.instances.undelete
      - bb.instances.update
      - bb.instanceRoles.get
      - bb.instanceRoles.list
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.create
      - bb.issues.get
      - bb.issues.list
      - bb.issues.update
      - bb.planCheckRuns.list
      - bb.planCheckRuns.run
      - bb.plans.create
      - bb.plans.get
      - bb.plans.list
      - bb.plans.preview
      - bb.plans.update
      - bb.policies.create
      - bb.policies.delete
      - bb.policies.get
      - bb.policies.list
      - bb.policies.update
      - bb.projects.create
      - bb.projects.delete
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.projects.list
      - bb.projects.setIamPolicy
      - bb.projects.undelete
      - bb.projects.update
      - bb.releases.check
      - bb.releases.create
      - bb.releases.delete
      - bb.releases.get
      - bb.releases.list
      - bb.releases.undelete
      - bb.releases.update
      - bb.reviewConfigs.create
      - bb.reviewConfigs.delete
      - bb.reviewConfigs.get
      - bb.reviewConfigs.list
      - bb.reviewConfigs.update
      - bb.revisions.create
      - bb.revisions.delete
      - bb.revisions.get
      - bb.revisions.list
      - bb.risks.create
      - bb.risks.delete
      - bb.risks.list
      - bb.risks.update
      - bb.roles.create
      - bb.roles.delete
      - bb.roles.list
      - bb.roles.get
      - bb.roles.update
      - bb.rollouts.create
      - bb.rollouts.get
      - bb.rollouts.list
      - bb.rollouts.preview
      - bb.settings.get
      - bb.settings.list
      - bb.settings.set
      - bb.sheets.create
      - bb.sheets.get
      - bb.sheets.update
      - bb.slowQueries.list
      - bb.sql.admin
      - bb.sql.ddl
      - bb.sql.dml
      - bb.sql.explain
      - bb.sql.info
      - bb.sql.export
      - bb.sql.select
      - bb.taskRuns.create
      - bb.taskRuns.list
      - bb.groups.get
      - bb.groups.list
      - bb.worksheets.get
      - bb.worksheets.manage
      - bb.workspaces.getIamPolicy
  - name: roles/workspaceMember
    title: Workspace member
    permissions:
      - bb.instanceRoles.get
      - bb.instanceRoles.list
      - bb.policies.get
      - bb.policies.list
      - bb.reviewConfigs.get
      - bb.reviewConfigs.list
      - bb.risks.list
      - bb.roles.list
      - bb.roles.get
      - bb.settings.get
      - bb.settings.list
      - bb.groups.create
      - bb.groups.get
      - bb.groups.list
      - bb.workspaces.getIamPolicy
  - name: roles/projectOwner
    title: Project owner
    permissions:
      - bb.auditLogs.export
      - bb.auditLogs.search
      - bb.changelists.create
      - bb.changelists.delete
      - bb.changelists.get
      - bb.changelists.list
      - bb.changelists.update
      - bb.changelogs.get
      - bb.changelogs.list
      - bb.databaseCatalogs.get
      - bb.databaseCatalogs.update
      - bb.databaseSecrets.delete
      - bb.databaseSecrets.list
      - bb.databaseSecrets.update
      - bb.databases.adviseIndex
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.databases.sync
      - bb.databases.update
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.create
      - bb.issues.get
      - bb.issues.list
      - bb.issues.update
      - bb.planCheckRuns.list
      - bb.planCheckRuns.run
      - bb.plans.create
      - bb.plans.get
      - bb.plans.list
      - bb.plans.preview
      - bb.plans.update
      - bb.policies.create
      - bb.policies.delete
      - bb.policies.get
      - bb.policies.list
      - bb.policies.update
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.projects.setIamPolicy
      - bb.projects.update
      - bb.releases.check
      - bb.releases.create
      - bb.releases.delete
      - bb.releases.get
      - bb.releases.list
      - bb.releases.undelete
      - bb.releases.update
      - bb.revisions.create
      - bb.revisions.delete
      - bb.revisions.get
      - bb.revisions.list
      - bb.rollouts.create
      - bb.rollouts.get
      - bb.rollouts.list
      - bb.rollouts.preview
      - bb.sheets.create
      - bb.sheets.get
      - bb.sheets.update
      - bb.slowQueries.list
      - bb.sql.ddl
      - bb.sql.dml
      - bb.sql.explain
      - bb.sql.info
      - bb.sql.export
      - bb.sql.select
      - bb.taskRuns.list
      - bb.worksheets.get
  - name: roles/projectDeveloper
    title: Project developer
    permissions:
      - bb.auditLogs.search
      - bb.changelists.create
      - bb.changelists.get
      - bb.changelists.list
      - bb.changelists.update
      - bb.changelogs.get
      - bb.changelogs.list
      - bb.databaseCatalogs.get
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.databases.sync
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.create
      - bb.issues.get
      - bb.issues.list
      - bb.issues.update
      - bb.planCheckRuns.list
      - bb.planCheckRuns.run
      - bb.plans.create
      - bb.plans.get
      - bb.plans.list
      - bb.plans.preview
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.releases.check
      - bb.releases.get
      - bb.releases.list
      - bb.revisions.get
      - bb.revisions.list
      - bb.rollouts.create
      - bb.rollouts.get
      - bb.rollouts.list
      - bb.rollouts.preview
      - bb.sheets.create
      - bb.sheets.get
      - bb.sheets.update
      - bb.slowQueries.list
      - bb.taskRuns.list
  - name: roles/sqlEditorUser
    title: SQL Editor User
    permissions:
      - bb.databaseCatalogs.get
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.sql.ddl
      - bb.sql.dml
      - bb.sql.explain
      - bb.sql.info
      - bb.sql.select
      - bb.worksheets.get
  - name: roles/projectExporter
    title: Project exporter
    permissions:
      - bb.databaseCatalogs.get
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.sql.export
      - bb.worksheets.get
  - name: roles/projectReleaser
    title: Project releaser
    permissions:
      - bb.databaseCatalogs.get
      - bb.changelogs.get
      - bb.databases.check
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.get
      - bb.issues.list
      - bb.planCheckRuns.list
      - bb.planCheckRuns.run
      - bb.plans.get
      - bb.plans.list
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.releases.get
      - bb.releases.list
      - bb.revisions.get
      - bb.revisions.list
      - bb.rollouts.get
      - bb.rollouts.list
      - bb.sheets.get
      - bb.taskRuns.create
      - bb.taskRuns.list
  - name: roles/projectViewer
    title: Project viewer
    permissions:
      - bb.databaseCatalogs.get
      - bb.databases.get
      - bb.databases.getSchema
      - bb.databases.list
      - bb.issueComments.create
      - bb.issueComments.list
      - bb.issueComments.update
      - bb.issues.create
      - bb.projects.get
      - bb.projects.getIamPolicy
      - bb.issues.get
