// Package oracle is the advisor for oracle database.
package oracle

import (
	"github.com/antlr4-go/antlr/v4"
	parser "github.com/bytebase/plsql-parser"

	"github.com/bytebase/bytebase/backend/plugin/advisor"
	"github.com/bytebase/bytebase/backend/plugin/advisor/db"
)

var (
	_ advisor.Advisor = (*%AdvisorName)(nil)
)

func init() {
	advisor.Register(storepb.Engine_ORACLE, advisor.%AdvisorType, &%AdvisorName{})
	advisor.Register(storepb.Engine_DM, advisor.%AdvisorType, &%AdvisorName{})
	advisor.Register(storepb.Engine_OCEANBASE_ORACLE, advisor.%AdvisorType, &%AdvisorName{})
}

// %AdvisorName is the advisor checking for %AdvisorComment
type %AdvisorName struct {
}

// Check checks for %AdvisorComment
func (*%AdvisorName) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	tree, ok := checkCtx.AST.(antlr.Tree)
	if !ok {
		return nil, errors.Errorf("failed to convert to Tree")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}

	listener := &%CheckerName{
		level:         level,
		title:         string(checkCtx.Rule.Type),
		currentSchema: ctx.CurrentSchema,
	}

	antlr.ParseTreeWalkerDefault.Walk(listener, tree)

	return listener.generateAdvice()
}

// %CheckerName is the listener for %AdvisorComment
type %CheckerName struct {
	*parser.BasePlSqlParserListener

	level         storepb.Advice_Status
	title         string
	currentSchema string
}

func (l *%CheckerName) generateAdvice() ([]*storepb.Advice, error) {
	advice := []*storepb.Advice{}

	return advice, nil
}
