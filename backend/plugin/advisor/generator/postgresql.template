package pg

// Framework code is generated by the generator.

import (
	"github.com/pkg/errors"

	"github.com/bytebase/bytebase/backend/plugin/advisor"
	"github.com/bytebase/bytebase/backend/plugin/parser/sql/ast"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*%AdvisorName)(nil)
	_ ast.Visitor     = (*%CheckerName)(nil)
)

func init() {
	advisor.Register(storepb.Engine_POSTGRES, advisor.%AdvisorType, &%AdvisorName{})
}

// %AdvisorName is the advisor checking for %AdvisorComment
type %AdvisorName struct {
}

// Check checks for %AdvisorComment
func (*%AdvisorName) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	stmtList, ok := checkCtx.AST.([]ast.Node)
	if !ok {
		return nil, errors.Errorf("failed to convert to Node")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}
	checker := &%CheckerName{
		level: level,
		title: string(checkCtx.Rule.Type),
	}

	for _, stmt := range stmtList {
        ast.Walk(checker, stmt)
	}

	return checker.adviceList, nil
}

type %CheckerName struct {
	adviceList []*storepb.Advice
	level      storepb.Advice_Status
	title      string
}

// Visit implements ast.Visitor interface.
func (checker *%CheckerName) Visit(in ast.Node) ast.Visitor {
	// TODO: implement it
	// switch node := in.(type) {
	// }
    
    return checker
}
