// Package mssql is the advisor for MSSQL database.
package mssql

import (
	"github.com/antlr4-go/antlr/v4"
	parser "github.com/bytebase/tsql-parser"
	"github.com/pkg/errors"

	"github.com/bytebase/bytebase/backend/plugin/advisor"
	"github.com/bytebase/bytebase/backend/plugin/advisor/db"
)

var (
	_ advisor.Advisor = (*%AdvisorName)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MSSQL, advisor.%AdvisorType, &%AdvisorName{})
}

// %AdvisorName is the advisor checking for %AdvisorComment.
type %AdvisorName struct {
}

// Check checks for %AdvisorComment.
func (*%AdvisorName) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	tree, ok := checkCtx.AST.(antlr.Tree)
	if !ok {
		return nil, errors.Errorf("failed to convert to Tree")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}

	listener := &%CheckerName{
		level: level,
		title: string(checkCtx.Rule.Type),
	}

	antlr.ParseTreeWalkerDefault.Walk(listener, tree)

	return listener.generateAdvice()
}

// %CheckerName is the listener for %AdvisorComment
type %CheckerName struct {
	*parser.BaseTSqlParserListener

	level         storepb.Advice_Status
	title         string

	adviceList []*storepb.Advice
}

// generateAdvice returns the advices generated by the listener, the advices must not be empty.
func (l *%CheckerName) generateAdvice() ([]*storepb.Advice, error) {
	return l.adviceList, nil
}
