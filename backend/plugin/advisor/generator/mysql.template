package mysql

// Framework code is generated by the generator.

import (
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	"github.com/bytebase/bytebase/backend/plugin/advisor/db"
	"github.com/pingcap/tidb/pkg/parser/ast"
)

var (
	_ advisor.Advisor = (*%AdvisorName)(nil)
	_ ast.Visitor     = (*%CheckerName)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MYSQL, advisor.%AdvisorType, &%AdvisorName{})
	advisor.Register(storepb.Engine_TIDB, advisor.%AdvisorType, &%AdvisorName{})
	advisor.Register(storepb.Engine_MARIADB, advisor.%AdvisorType, &%AdvisorName{})
	advisor.Register(storepb.Engine_OCEANBASE, advisor.%AdvisorType, &%AdvisorName{})
}

// %AdvisorName is the advisor checking for %AdvisorComment
type %AdvisorName struct {
}

// Check checks for %AdvisorComment
func (*%AdvisorName) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	stmtList, ok := checkCtx.AST.([]ast.StmtNode)
	if !ok {
		return nil, errors.Errorf("failed to convert to StmtNode")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}
	checker := &%CheckerName{
		level: level,
		title: string(checkCtx.Rule.Type),
	}

	for _, stmt := range stmtList {
		checker.text = stmt.Text()
		checker.line = stmt.OriginTextPosition()
		(stmt).Accept(checker)
	}

	return checker.adviceList, nil
}

type %CheckerName struct {
	adviceList []*storepb.Advice
	level      storepb.Advice_Status
	title      string
	text       string
	line       int
}

// Enter implements the ast.Visitor interface.
func (*%CheckerName) Enter(in ast.Node) (ast.Node, bool) {
	// TODO: implement it
	// switch node := in.(type) {
	// }

	return in, false
}

// Leave implements the ast.Visitor interface.
func (*%CheckerName) Leave(in ast.Node) (ast.Node, bool) {
	return in, true
}
