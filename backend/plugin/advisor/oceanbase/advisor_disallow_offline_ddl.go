package oceanbase

// Framework code is generated by the generator.

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/antlr4-go/antlr/v4"
	"github.com/pkg/errors"

	mysql "github.com/bytebase/mysql-parser"

	"github.com/bytebase/bytebase/backend/common"
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	mysqlparser "github.com/bytebase/bytebase/backend/plugin/parser/mysql"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*DisallowOfflineDdlAdvisor)(nil)
)

func init() {
	advisor.Register(storepb.Engine_OCEANBASE, advisor.MySQLDisallowOfflineDDL, &DisallowOfflineDdlAdvisor{})
}

// DisallowOfflineDdlAdvisor is the advisor checking for disallow Offline DDL.
type DisallowOfflineDdlAdvisor struct {
}

// Check checks for disallow Offline DDL.
func (*DisallowOfflineDdlAdvisor) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	stmtList, ok := checkCtx.AST.([]*mysqlparser.ParseResult)
	if !ok {
		return nil, errors.Errorf("failed to convert to mysql parser result")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}

	checker := &disallowOfflineDdlChecker{
		level:     level,
		title:     checkCtx.Rule.Type,
		driver:    checkCtx.Driver,
		currentDB: checkCtx.CurrentDatabase,
	}

	for _, stmtNode := range stmtList {
		checker.baseLine = stmtNode.BaseLine
		antlr.ParseTreeWalkerDefault.Walk(checker, stmtNode.Tree)
	}

	return checker.adviceList, nil
}

type disallowOfflineDdlChecker struct {
	*mysql.BaseMySQLParserListener

	baseLine   int
	adviceList []*storepb.Advice
	level      storepb.Advice_Status
	title      string
	driver     *sql.DB
	currentDB  string
}

// EnterAlterTable is called when production alterTable is entered.
func (checker *disallowOfflineDdlChecker) EnterAlterTable(ctx *mysql.AlterTableContext) {
	if !mysqlparser.IsTopMySQLRule(&ctx.BaseParserRuleContext) {
		return
	}
	if ctx.TableRef() == nil {
		return
	}

	if ctx.AlterTableActions() == nil {
		return
	}

	if ctx.AlterTableActions().PartitionClause() != nil && ctx.AlterTableActions().PartitionClause().PartitionTypeDef() != nil {
		checker.advice(ctx, "Modifying partitions")
		return
	}

	if ctx.AlterTableActions().AlterCommandList() == nil || ctx.AlterTableActions().AlterCommandList().AlterList() == nil {
		return
	}

	databaseName, tableName := mysqlparser.NormalizeMySQLTableRef(ctx.TableRef())
	for _, item := range ctx.AlterTableActions().AlterCommandList().AlterList().AllAlterListItem() {
		if item == nil {
			continue
		}

		switch {
		case item.ADD_SYMBOL() != nil:
			switch {
			// add one column
			case item.Identifier() != nil && item.FieldDefinition() != nil:
				if columnType := checker.getSpecialColumnType(item.FieldDefinition()); len(columnType) != 0 {
					checker.advice(ctx, fmt.Sprintf("Adding %s columns", columnType))
				}
			// add multiple columns
			case item.OPEN_PAR_SYMBOL() != nil && item.TableElementList() != nil:
				for _, tableElement := range item.TableElementList().AllTableElement() {
					if tableElement.ColumnDefinition() == nil {
						continue
					}
					if tableElement.ColumnDefinition().FieldDefinition() == nil {
						continue
					}
					if columnType := checker.getSpecialColumnType(item.FieldDefinition()); len(columnType) != 0 {
						checker.advice(ctx, fmt.Sprintf("Adding %s columns", columnType))
					}
				}
			// add primary key
			case item.TableConstraintDef() != nil:
				if item.TableConstraintDef().PRIMARY_SYMBOL() != nil && item.TableConstraintDef().KEY_SYMBOL() != nil {
					checker.advice(ctx, "Adding primary keys")
				}
			}
		// modify column
		case item.MODIFY_SYMBOL() != nil && item.ColumnInternalRef() != nil && item.FieldDefinition() != nil:
			if columnType := checker.getSpecialColumnType(item.FieldDefinition()); len(columnType) != 0 {
				checker.advice(ctx, fmt.Sprintf("Modifying columns to %s", columnType))
			}
		// change column
		case item.CHANGE_SYMBOL() != nil && item.ColumnInternalRef() != nil && item.FieldDefinition() != nil:
			if columnType := checker.getSpecialColumnType(item.FieldDefinition()); len(columnType) != 0 {
				checker.advice(ctx, fmt.Sprintf("Changing columns to %s", columnType))
			}
		case item.DROP_SYMBOL() != nil:
			switch {
			// drop column
			case item.ColumnInternalRef() != nil:
				if len(databaseName) == 0 {
					databaseName = checker.currentDB
				}
				columnName := mysqlparser.NormalizeMySQLColumnInternalRef(item.ColumnInternalRef())
				if checker.isStoredColumn(databaseName, tableName, columnName) {
					checker.advice(ctx, "Dropping stored generated columns")
				}
			// drop primary key
			case item.PRIMARY_SYMBOL() != nil && item.KEY_SYMBOL() != nil:
				checker.advice(ctx, "Dropping primary keys")
			}
		// change charset or collate
		case item.Charset() != nil || item.Collate() != nil:
			checker.advice(ctx, "Changing charset or collate")
		}
	}
}

func (checker *disallowOfflineDdlChecker) isStoredColumn(databaseName, tableName, columnName string) bool {
	if len(databaseName) == 0 || len(tableName) == 0 || len(columnName) == 0 {
		return false
	}

	var result string
	if err := checker.driver.QueryRowContext(context.Background(),
		"SELECT EXTRA FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND TABLE_NAME = ? AND COLUMN_NAME = ?",
		databaseName,
		tableName,
		columnName,
	).Scan(&result); err == nil && strings.Contains(strings.ToLower(result), "stored") {
		return true
	}
	return false
}

// EnterAlterPartition is called when entering the alterPartition production.
func (checker *disallowOfflineDdlChecker) EnterAlterPartition(ctx *mysql.AlterPartitionContext) {
	if ctx.PARTITION_SYMBOL() == nil {
		return
	}

	switch {
	case ctx.DROP_SYMBOL() != nil:
		checker.advice(ctx, "Dropping partitions")
	case ctx.TRUNCATE_SYMBOL() != nil:
		checker.advice(ctx, "Truncating partitions")
	}
}

// EnterDropStatement is called when entering the dropStatement production.
func (checker *disallowOfflineDdlChecker) EnterDropStatement(ctx *mysql.DropStatementContext) {
	if !mysqlparser.IsTopMySQLRule(&ctx.BaseParserRuleContext) {
		return
	}
	if ctx.DropTable() != nil {
		checker.advice(ctx, "Dropping tables")
	}
}

// EnterTruncateTableStatement is called when entering the truncateTableStatement production.
func (checker *disallowOfflineDdlChecker) EnterTruncateTableStatement(ctx *mysql.TruncateTableStatementContext) {
	if !mysqlparser.IsTopMySQLRule(&ctx.BaseParserRuleContext) {
		return
	}
	if ctx.TableRef() != nil {
		checker.advice(ctx, "Truncating tables")
	}
}

func (checker *disallowOfflineDdlChecker) getSpecialColumnType(ctx mysql.IFieldDefinitionContext) string {
	if ctx == nil {
		return ""
	}
	switch {
	case ctx.STORED_SYMBOL() != nil:
		return "stored generated"
	case checker.isAutoIncrementColumn(ctx):
		return "auto increment"
	case checker.isPrimaryKeyColumn(ctx):
		return "primary key"
	default:
		return ""
	}
}

func (*disallowOfflineDdlChecker) isAutoIncrementColumn(ctx mysql.IFieldDefinitionContext) bool {
	for _, attr := range ctx.AllColumnAttribute() {
		if attr.AUTO_INCREMENT_SYMBOL() != nil {
			return true
		}
	}
	return false
}

func (*disallowOfflineDdlChecker) isPrimaryKeyColumn(ctx mysql.IFieldDefinitionContext) bool {
	for _, attr := range ctx.AllColumnAttribute() {
		if attr.PRIMARY_SYMBOL() != nil && attr.KEY_SYMBOL() != nil {
			return true
		}
	}
	return false
}

func (checker *disallowOfflineDdlChecker) advice(ctx antlr.ParserRuleContext, operation string) {
	checker.adviceList = append(checker.adviceList, &storepb.Advice{
		Status:        checker.level,
		Code:          advisor.StatementOfflineDDL.Int32(),
		Title:         checker.title,
		Content:       fmt.Sprintf("%s is an offline DDL operation.", operation),
		StartPosition: common.ConvertANTLRLineToPosition(checker.baseLine + ctx.GetStart().GetLine()),
	})
}
