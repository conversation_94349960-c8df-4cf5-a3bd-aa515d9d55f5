- statement: CREATE TABLE t(id INT PRIMARY KEY)
  changeType: 1
- statement: CREATE TABLE t(id INT, PRIMARY KEY (id))
  changeType: 1
- statement: CREATE TABLE t(id INT)
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: 'Table "public"."t" requires PRIMARY KEY, related statement: "CREATE TABLE t(id INT)"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE "tech_book" DROP CONSTRAINT "old_pk"
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: 'Table "public"."tech_book" requires PRIMARY KEY, related statement: "ALTER TABLE \"tech_book\" DROP CONSTRAINT \"old_pk\""'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE "tech_book" DROP CONSTRAINT "old_index"
  changeType: 1
- statement: ALTER TABLE "tech_book" DROP CONSTRAINT constraint_not_in_catalog
  changeType: 1
- statement: ALTER TABLE "tech_book" DROP COLUMN id
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: 'Table "public"."tech_book" requires PRIMARY KEY, related statement: "ALTER TABLE \"tech_book\" DROP COLUMN id"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN column_not_in_pk int;
    ALTER TABLE "tech_book" DROP COLUMN column_not_in_pk;
  changeType: 1
