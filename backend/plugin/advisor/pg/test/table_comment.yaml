- statement: |-
    CREATE TABLE t(a int);
    COMMENT ON TABLE t IS 'comments';
  changeType: 1
- statement: CREATE TABLE t(a int);
  changeType: 1
  want:
    - status: 2
      code: 1032
      title: table.comment
      content: Comment is required for table `t`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    CREATE TABLE d(a int);
    COMMENT ON TABLE t IS 'comments';
  changeType: 1
  want:
    - status: 2
      code: 1032
      title: table.comment
      content: Comment is required for table `d`
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    COMMENT ON TABLE t IS 'comments';
    COMMENT ON TABLE t IS '';
  changeType: 1
  want:
    - status: 2
      code: 1032
      title: table.comment
      content: Comment is required for table `t`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    COMMENT ON TABLE t IS 'comments';
    COMMENT ON TABLE t IS NULL;
  changeType: 1
  want:
    - status: 2
      code: 1032
      title: table.comment
      content: Comment is required for table `t`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    COMMENT ON TABLE t IS 'looooooong comments';
  changeType: 1
  want:
    - status: 2
      code: 1301
      title: table.comment
      content: Table `t` comment is too long. The length of comment should be within 10 characters
      startposition:
        line: 1
        column: 0
      endposition: null
