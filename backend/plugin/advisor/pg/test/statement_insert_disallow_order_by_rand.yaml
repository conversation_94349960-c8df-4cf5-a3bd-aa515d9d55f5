- statement: INSERT INTO tech_book VALUES(1, "a")
  changeType: 1
- statement: INSERT INTO tech_book SELECT * FROM tech_book ORDER BY random()
  changeType: 1
  want:
    - status: 2
      code: 1108
      title: statement.insert.disallow-order-by-rand
      content: The INSERT statement uses ORDER BY random() or random_between(), related statement "INSERT INTO tech_book SELECT * FROM tech_book ORDER BY random()"
      startposition:
        line: 0
        column: 0
      endposition: null
