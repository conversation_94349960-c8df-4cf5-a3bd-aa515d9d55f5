- statement: |-
    START TRANSACTION;
    CREATE TABLE techBook(id int, name varchar(255));
    COMMIT;
  changeType: 1
  want:
    - status: 2
      code: 206
      title: statement.disallow-commit
      content: 'Commit is not allowed, related statement: "COMMIT;"'
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    START TRANSACTION;
    CREATE TABLE techBook(id int, name varchar(255));
    COMMIT;

    START TRANSACTION;
    INSERT INTO techBook(id, name) VALUES(1, "book");
    COMMIT;
  changeType: 1
  want:
    - status: 2
      code: 206
      title: statement.disallow-commit
      content: 'Commit is not allowed, related statement: "COMMIT;"'
      startposition:
        line: 2
        column: 0
      endposition: null
    - status: 2
      code: 206
      title: statement.disallow-commit
      content: 'Commit is not allowed, related statement: "COMMIT;"'
      startposition:
        line: 6
        column: 0
      endposition: null
