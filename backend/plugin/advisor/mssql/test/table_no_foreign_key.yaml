- statement: |-
    CREATE TABLE MyTable
    (
        Id            INT REFERENCES Person(ID),
        FullName      VARCHAR(10),
        POSITION      VARCHAR(100)
    );
  changeType: 1
  want:
    - status: 2
      code: 602
      title: table.no-foreign-key
      content: FOREIGN KEY is not allowed in the table MyTable.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE Customer
    (
        Id            INT,
        FullName      VARCHAR(10),
        Position      VARCHAR(100),
        FOREIGN KEY (Id) REFERENCES PERSON(Id)
    );
  changeType: 1
  want:
    - status: 2
      code: 602
      title: table.no-foreign-key
      content: FOREIGN KEY is not allowed in the table Customer.
      startposition:
        line: 5
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE Customer
    (
        Id            INT,
        FullName      VARCHAR(10),
        Position      VARCHAR(100)
    );
    ALTER TABLE Customer ADD FOREIGN KEY (Id) REFERENCES Person(Id);
  changeType: 1
  want:
    - status: 2
      code: 602
      title: table.no-foreign-key
      content: <PERSON>OREIGN KEY is not allowed in the table Customer.
      startposition:
        line: 6
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE Customer
    (
        Id            INT,
        FullName      VARCHAR(10),
        Position      VARCHAR(100)
    );
    CREATE TABLE Person
    (
        Id            INT,
        FullName      VARCHAR(10),
        Position      VARCHAR(100)
    );
  changeType: 1
