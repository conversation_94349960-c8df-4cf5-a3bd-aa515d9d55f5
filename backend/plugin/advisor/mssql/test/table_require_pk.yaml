- statement: |-
    CREATE TABLE MySchema.MyTable (
      Id INT NOT NULL,
      Name VARCHAR(50) NOT NULL
    );
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table MySchema.MyTable requires PRIMARY KEY.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE MySchema.MyTable (
      Id INT NOT NULL PRIMARY KEY,
      Name VARCHAR(50) NOT NULL
    );
  changeType: 1
- statement: |-
    CREATE TABLE MySchema.MyTable (
      Id INT NOT NULL,
      Name VARCHAR(50) NOT NULL,
      PRIMARY KEY (Id)
    );
  changeType: 1
- statement: ALTER TABLE MySchema.MyTable ADD CONSTRAINT PK_MyTable PRIMARY KEY (Id);
  changeType: 1
