- statement: |-
    CREATE FUNCTION dbo.ufnGetInventoryStock(@ProductID int)
    RETURNS int
    AS
    -- Returns the stock level for the product.
    BEGIN
        DECLARE @ret int;
        SELECT @ret = SUM(p.Quantity)
        FROM Production.ProductInventory p
        WHERE p.ProductID = @ProductID
            AND p.LocationID = '6';
        IF (@ret IS NULL)
            SET @ret = 0;
        RETURN @ret;
    END;
  changeType: 1
  want:
    - status: 2
      code: 1701
      title: system.function.disallow-create
      content: Creating or altering functions is prohibited
      startposition:
        line: 0
        column: 0
      endposition: null
