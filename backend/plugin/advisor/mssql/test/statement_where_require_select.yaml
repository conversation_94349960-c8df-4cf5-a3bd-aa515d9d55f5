- statement: SELECT * FROM MyTable;
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.select
      content: WHERE clause is required for SELETE statement.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT * FROM MyTable WHERE a = 1;
  changeType: 1
- statement: |-
    WITH CteA AS (SELECT * FROM TableA WHERE a = 1),
          CteB AS (SELECT * FROM TableB)
    SELECT * FROM CteA INNER JOIN CteB ON CteA.a = CteB.b;
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.select
      content: WHERE clause is required for SELETE statement.
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 202
      title: statement.where.require.select
      content: WHERE clause is required for SELETE statement.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: SELECT 1;
  changeType: 1
