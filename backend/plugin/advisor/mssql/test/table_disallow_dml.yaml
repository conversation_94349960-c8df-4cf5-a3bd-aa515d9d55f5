- statement: |-
    <PERSON><PERSON><PERSON> INTO MySchema.Identifier USING MySchema.MyTable ON Identifier.Id = MyTable.Id
    WHEN MATCHED THEN UPDATE SET Identifier.Name = MyTable.Name
    WHEN NOT MATCHED THEN INSERT (Id, Name) VALUES (MyTable.Id, MyTable.Name);
  changeType: 1
  want:
    - status: 2
      code: 614
      title: table.disallow-dml
      content: DML is disallowed on table MySchema.Identifier.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    INSERT INTO MySchema.MyTable (Id, Name) VALUES (1, 'Alice');
    INSERT INTO MySchema.Identifier (Id, Name) VALUES (1, 'Alice');
  changeType: 1
  want:
    - status: 2
      code: 614
      title: table.disallow-dml
      content: DML is disallowed on table MySchema.Identifier.
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: DELETE FROM MySchema.Identifier WHERE Id = 1;
  changeType: 1
  want:
    - status: 2
      code: 614
      title: table.disallow-dml
      content: DML is disallowed on table MySchema.Identifier.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: UPDATE MySchema.Identifier SET Name = 'Bob' WHERE Id = 1;
  changeType: 1
  want:
    - status: 2
      code: 614
      title: table.disallow-dml
      content: DML is disallowed on table MySchema.Identifier.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT * INTO MySchema.Identifier FROM MySchema.MyTable;
  changeType: 1
  want:
    - status: 2
      code: 614
      title: table.disallow-dml
      content: DML is disallowed on table MySchema.Identifier.
      startposition:
        line: 0
        column: 0
      endposition: null
