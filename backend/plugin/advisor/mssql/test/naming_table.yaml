- statement: CREATE TABLE ALL_UPPERCASE(ID INT);
  changeType: 1
- statement: CREATE TABLE [\\](ID INT);
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '[\\] mismatches table naming convention, naming format should be "^[A-Z]([_A-Za-z])*$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE all_lowercase(ID INT);
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: all_lowercase mismatches table naming convention, naming format should be "^[A-Z]([_A-Za-z])*$"
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE PascalCase(ID INT);
  changeType: 1
- statement: execute sp_rename 'Sales.SalesTerritory', 'SALESTERR';
  changeType: 1
- statement: execute sp_rename 'Sales.SalesTerritory', 'salesTerr';
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: salesTerr mismatches table naming convention, naming format should be "^[A-Z]([_A-Za-z])*$"
      startposition:
        line: 0
        column: 0
      endposition: null
