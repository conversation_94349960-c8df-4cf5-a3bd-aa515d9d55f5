- statement: |-
    CREATE TABLE pokes(foo int);
    SELECT foo FROM pokes WHERE (foo + 1) * 2 > 0;
    INSERT INTO pokes VALUES(1);
    SELECT foo FROM pokes WHERE ~foo > 0;
  changeType: 1
  want:
    - status: 2
      code: 234
      title: statement.where.disallow-functions-and-calculations
      content: Performing calculations in 'WHERE' clause is not allowed
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 234
      title: statement.where.disallow-functions-and-calculations
      content: Performing calculations in 'WHERE' clause is not allowed
      startposition:
        line: 3
        column: 0
      endposition: null
- statement: |-
    SELECT foo FROM pokes WHERE foo | -foo > 0;
    SELECT bar FROM pokes WHERE bar > AVG(bar);
  changeType: 1
  want:
    - status: 2
      code: 234
      title: statement.where.disallow-functions-and-calculations
      content: Performing calculations in 'WHERE' clause is not allowed
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 234
      title: statement.where.disallow-functions-and-calculations
      content: Calling function 'AVG(bar)' in 'WHERE' clause is not allowed
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: SELECT bar FROM pokes WHERE bar > 1;
  changeType: 1
- statement: |-
    SELECT Department, AVG(Salary) AS AvgSalary
    FROM Employees
    GROUP BY Department
    HAVING AVG(Salary) * 3 + 1 > 50000;
  changeType: 1
- statement: select * from (select * from t where t.a > 1) t1 where a > AVG(a);
  changeType: 1
  want:
    - status: 2
      code: 234
      title: statement.where.disallow-functions-and-calculations
      content: Calling function 'AVG(a)' in 'WHERE' clause is not allowed
      startposition:
        line: 0
        column: 0
      endposition: null
