- statement: |-
    CREATE TABLE MyTable (
      Id INT PRIMARY KEY,
      Name VARCHAR(100) NOT NULL,
      Age INT,
      Address VARCHAR(200)
    );
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column [age] is nullable, which is not allowed.
      startposition:
        line: 3
        column: 0
      endposition: null
    - status: 2
      code: 402
      title: column.no-null
      content: Column [address] is nullable, which is not allowed.
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE MyTable (
      Id INT,
      Name VARCHAR(100) NOT NULL,
      Age INT,
      Address VARCHAR(200),
      PRIMARY KEY (Id, Age)
    );
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column [address] is nullable, which is not allowed.
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: ALTER TABLE MyTable ALTER COLUMN Name VARCHAR(100) NULL;
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column [name] is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE MyTable ALTER COLUMN Name VARCHAR(100) NOT NULL;
  changeType: 1
- statement: ALTER TABLE MyTable ADD Age INT;
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column [age] is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE MyTable ADD Age INT NULL;
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column [age] is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE MyTable ADD Age INT NOT NULL;
  changeType: 1
