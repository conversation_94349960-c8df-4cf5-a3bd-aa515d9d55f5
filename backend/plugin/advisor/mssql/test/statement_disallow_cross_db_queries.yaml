- statement: SELECT * FROM MASTER.dbo.pokes;
  changeType: 1
- statement: |-
    WITH foo1 AS (SELECT * FROM master2.dbo.tech_book)
    SELECT * FROM master.dbo.pokes;
    CREATE TABLE pokes (foo int);
  changeType: 1
  want:
    - status: 2
      code: 233
      title: statement.disallow-cross-db-queries
      content: 'Cross database queries (target databse: ''master2'', current database: ''master'') are prohibited'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    USE master2;
    SELECT * FROM master.dbo.pokes;
  changeType: 1
  want:
    - status: 2
      code: 233
      title: statement.disallow-cross-db-queries
      content: 'Cross database queries (target databse: ''master'', current database: ''master2'') are prohibited'
      startposition:
        line: 1
        column: 0
      endposition: null
