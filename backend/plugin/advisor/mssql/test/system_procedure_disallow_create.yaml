- statement: |-
    CREATE PROCEDURE SalesLT.uspGetCustomerCompany
    (
        -- Add the parameters for the stored procedure here
        @LastName nvarchar(50) = NULL,
        @FirstName nvarchar(50) = NULL
    )
    AS
    BEGIN
        -- SET NOCOUNT ON added to prevent extra result sets from
        -- interfering with SELECT statements.
        SET NOCOUNT ON

        -- Insert statements for procedure here
        SELECT FirstName, LastName, CompanyName
          FROM SalesLT.Customer
          WHERE FirstName = @FirstName AND LastName = @LastName;
    END
    GO
  changeType: 1
  want:
    - status: 2
      code: 1401
      title: system.procedure.disallow-create
      content: Creating or altering procedures is prohibited
      startposition:
        line: 0
        column: 0
      endposition: null
