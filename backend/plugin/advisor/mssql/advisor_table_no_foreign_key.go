// Package mssql is the advisor for MSSQL database.
package mssql

import (
	"context"
	"fmt"

	"github.com/antlr4-go/antlr/v4"
	parser "github.com/bytebase/tsql-parser"
	"github.com/pkg/errors"

	"github.com/bytebase/bytebase/backend/common"
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	tsqlparser "github.com/bytebase/bytebase/backend/plugin/parser/tsql"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*TableNoForeignKeyAdvisor)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MSSQL, advisor.MSSQLTableNoFK, &TableNoForeignKeyAdvisor{})
}

// TableNoForeignKeyAdvisor is the advisor checking for table disallow foreign key..
type TableNoForeignKeyAdvisor struct {
}

// Check checks for table disallow foreign key..
func (*TableNoForeignKeyAdvisor) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	tree, ok := checkCtx.AST.(antlr.Tree)
	if !ok {
		return nil, errors.Errorf("failed to convert to Tree")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}

	listener := &tableNoForeignKeyChecker{
		level:                      level,
		title:                      string(checkCtx.Rule.Type),
		currentNormalizedTableName: "",
		tableHasForeignKey:         make(map[string]bool),
		tableOriginalName:          make(map[string]string),
		tableLine:                  make(map[string]int),
	}

	antlr.ParseTreeWalkerDefault.Walk(listener, tree)

	return listener.generateAdvice()
}

// tableNoForeignKeyChecker is the listener for table disallow foreign key.
type tableNoForeignKeyChecker struct {
	*parser.BaseTSqlParserListener

	level storepb.Advice_Status
	title string

	// currentNormalizedTableName is the normalized table name of the current table.
	currentNormalizedTableName string
	// currentConstraintAction is the current constraint action.
	currentConstraintAction currentConstraintAction
	// tableHasForeignKey is true if the current table has foreign key.
	tableHasForeignKey map[string]bool
	// tableOriginalName is the original table name of the current table.
	tableOriginalName map[string]string
	// tableLine is the line number of the current table.
	tableLine map[string]int

	adviceList []*storepb.Advice
}

// generateAdvice returns the advices generated by the listener, the advices must not be empty.
func (l *tableNoForeignKeyChecker) generateAdvice() ([]*storepb.Advice, error) {
	for tableName, hasForeignKey := range l.tableHasForeignKey {
		if hasForeignKey {
			l.adviceList = append(l.adviceList, &storepb.Advice{
				Status:        l.level,
				Code:          advisor.TableHasFK.Int32(),
				Title:         l.title,
				Content:       fmt.Sprintf("FOREIGN KEY is not allowed in the table %s.", l.tableOriginalName[tableName]),
				StartPosition: common.ConvertANTLRLineToPosition(l.tableLine[tableName]),
			})
		}
	}
	return l.adviceList, nil
}

func (l *tableNoForeignKeyChecker) EnterCreate_table(ctx *parser.Create_tableContext) {
	tableName := ctx.Table_name()
	if tableName == nil {
		return
	}
	normalizedTableName := tsqlparser.NormalizeTSQLTableName(tableName, "" /* fallbackDatabase */, "dbo" /* fallbackSchema */, false /* caseSensitive */)

	l.tableHasForeignKey[normalizedTableName] = false
	l.tableOriginalName[normalizedTableName] = tableName.GetText()
	l.tableLine[normalizedTableName] = tableName.GetStart().GetLine()

	l.currentNormalizedTableName = normalizedTableName
	l.currentConstraintAction = currentConstraintActionAdd
}

func (l *tableNoForeignKeyChecker) ExitCreate_table(*parser.Create_tableContext) {
	l.currentNormalizedTableName = ""
	l.currentConstraintAction = currentConstraintActionNone
}

// EnterColumn_def_table_constraints will be called when entering "column_def_table_constraints" rule.
func (l *tableNoForeignKeyChecker) EnterColumn_def_table_constraints(ctx *parser.Column_def_table_constraintsContext) {
	if l.currentNormalizedTableName == "" {
		return
	}

	allColumnDefTableConstraints := ctx.AllColumn_def_table_constraint()
	for _, columnDefTableConstraint := range allColumnDefTableConstraints {
		if v := columnDefTableConstraint.Column_definition(); v != nil {
			allColumnDefinitionElements := v.AllColumn_definition_element()
			for _, columnDefinitionElement := range allColumnDefinitionElements {
				if v := columnDefinitionElement.Column_constraint(); v != nil {
					if v.Foreign_key_options() != nil {
						if l.currentConstraintAction == currentConstraintActionAdd {
							l.tableHasForeignKey[l.currentNormalizedTableName] = true
							l.tableLine[l.currentNormalizedTableName] = v.Foreign_key_options().GetStart().GetLine()
						}
						return
					}
				}
			}
		} else if v := columnDefTableConstraint.Table_constraint(); v != nil {
			if v.Foreign_key_options() != nil {
				if l.currentConstraintAction == currentConstraintActionAdd {
					l.tableHasForeignKey[l.currentNormalizedTableName] = true
					l.tableLine[l.currentNormalizedTableName] = v.Foreign_key_options().GetStart().GetLine()
				}
				return
			}
		}
	}
}

func (l *tableNoForeignKeyChecker) EnterAlter_table(ctx *parser.Alter_tableContext) {
	tableName := ctx.Table_name(0)
	if tableName == nil {
		return
	}
	normalizedTableName := tsqlparser.NormalizeTSQLTableName(tableName, "" /* fallbackDatabase */, "dbo" /* fallbackSchema */, false /* caseSensitive */)
	if ctx.ADD() != nil && ctx.Column_def_table_constraints() != nil {
		l.currentNormalizedTableName = normalizedTableName
		l.currentConstraintAction = currentConstraintActionAdd
	} else if ctx.DROP() != nil && ctx.CONSTRAINT() != nil && ctx.GetConstraint() != nil {
		l.currentNormalizedTableName = normalizedTableName
		l.currentConstraintAction = currentConstraintActionDrop
	}
}

func (l *tableNoForeignKeyChecker) ExitAlter_table(*parser.Alter_tableContext) {
	l.currentNormalizedTableName = ""
	l.currentConstraintAction = currentConstraintActionNone
}
