// Package mssql is the advisor for MSSQL database.
package mssql

import (
	"context"

	"github.com/antlr4-go/antlr/v4"
	parser "github.com/bytebase/tsql-parser"
	"github.com/pkg/errors"

	"github.com/bytebase/bytebase/backend/common"
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*SelectNoSelectAllAdvisor)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MSSQL, advisor.MSSQLNoSelectAll, &SelectNoSelectAllAdvisor{})
}

// SelectNoSelectAllAdvisor is the advisor checking for no select all.
type SelectNoSelectAllAdvisor struct {
}

// Check checks for no select all.
func (*SelectNoSelectAllAdvisor) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	tree, ok := checkCtx.AST.(antlr.Tree)
	if !ok {
		return nil, errors.Errorf("failed to convert to Tree")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}

	listener := &selectNoSelectAllChecker{
		level: level,
		title: string(checkCtx.Rule.Type),
	}

	antlr.ParseTreeWalkerDefault.Walk(listener, tree)

	return listener.generateAdvice()
}

// selectNoSelectAllChecker is the listener for no select all.
type selectNoSelectAllChecker struct {
	*parser.BaseTSqlParserListener

	level storepb.Advice_Status
	title string

	adviceList []*storepb.Advice
}

// generateAdvice returns the advices generated by the listener, the advices must not be empty.
func (l *selectNoSelectAllChecker) generateAdvice() ([]*storepb.Advice, error) {
	return l.adviceList, nil
}

// EnterSelect_list_elem enters the select_list_elem parser rule.
func (l *selectNoSelectAllChecker) EnterSelect_list_elem(ctx *parser.Select_list_elemContext) {
	if v := ctx.Asterisk(); v != nil {
		if v.STAR() != nil {
			l.adviceList = append(l.adviceList, &storepb.Advice{
				Status:        l.level,
				Code:          advisor.StatementSelectAll.Int32(),
				Title:         l.title,
				Content:       "Avoid using SELECT *.",
				StartPosition: common.ConvertANTLRLineToPosition(v.STAR().GetSymbol().GetLine()),
			})
		}
	}
}
