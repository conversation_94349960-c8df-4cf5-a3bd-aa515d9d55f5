package mysql

// Framework code is generated by the generator.

import (
	"context"
	"fmt"

	"github.com/antlr4-go/antlr/v4"
	"github.com/pkg/errors"

	mysql "github.com/bytebase/mysql-parser"

	"github.com/bytebase/bytebase/backend/common"
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	mysqlparser "github.com/bytebase/bytebase/backend/plugin/parser/mysql"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*DisallowOrderByAdvisor)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MYSQL, advisor.MySQLDisallowOrderBy, &DisallowOrderByAdvisor{})
	advisor.Register(storepb.Engine_MARIADB, advisor.MySQLDisallowOrderBy, &DisallowOrderByAdvisor{})
	advisor.Register(storepb.Engine_OCEANBASE, advisor.MySQLDisallowOrderBy, &DisallowOrderByAdvisor{})
}

// DisallowOrderByAdvisor is the advisor checking for no ORDER BY clause in DELETE/UPDATE statements.
type DisallowOrderByAdvisor struct {
}

// Check checks for no ORDER BY clause in DELETE/UPDATE statements.
func (*DisallowOrderByAdvisor) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	stmtList, ok := checkCtx.AST.([]*mysqlparser.ParseResult)
	if !ok {
		return nil, errors.Errorf("failed to convert to mysql parser result")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}
	checker := &disallowOrderByChecker{
		level: level,
		title: string(checkCtx.Rule.Type),
	}

	for _, stmt := range stmtList {
		checker.baseLine = stmt.BaseLine
		antlr.ParseTreeWalkerDefault.Walk(checker, stmt.Tree)
	}

	return checker.adviceList, nil
}

type disallowOrderByChecker struct {
	*mysql.BaseMySQLParserListener

	baseLine   int
	adviceList []*storepb.Advice
	level      storepb.Advice_Status
	title      string
	text       string
	line       int
}

func (checker *disallowOrderByChecker) EnterQuery(ctx *mysql.QueryContext) {
	checker.text = ctx.GetParser().GetTokenStream().GetTextFromRuleContext(ctx)
}

// EnterDeleteStatement is called when production deleteStatement is entered.
func (checker *disallowOrderByChecker) EnterDeleteStatement(ctx *mysql.DeleteStatementContext) {
	if ctx.OrderClause() != nil && ctx.OrderClause().ORDER_SYMBOL() != nil {
		checker.handleOrderByClause(advisor.DeleteUseOrderBy, ctx.GetStart().GetLine())
	}
}

// EnterUpdateStatement is called when production updateStatement is entered.
func (checker *disallowOrderByChecker) EnterUpdateStatement(ctx *mysql.UpdateStatementContext) {
	if ctx.OrderClause() != nil && ctx.OrderClause().ORDER_SYMBOL() != nil {
		checker.handleOrderByClause(advisor.UpdateUseOrderBy, ctx.GetStart().GetLine())
	}
}

func (checker *disallowOrderByChecker) handleOrderByClause(code advisor.Code, lineNumber int) {
	checker.adviceList = append(checker.adviceList, &storepb.Advice{
		Status:        checker.level,
		Code:          code.Int32(),
		Title:         checker.title,
		Content:       fmt.Sprintf("ORDER BY clause is forbidden in DELETE and UPDATE statements, but \"%s\" uses", checker.text),
		StartPosition: common.ConvertANTLRLineToPosition(checker.line + lineNumber),
	})
}
