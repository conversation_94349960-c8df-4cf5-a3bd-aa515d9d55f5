- statement: |
    DELETE FROM tech_book WHERE name like "%abc";
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: '"DELETE FROM tech_book WHERE name like "%abc";" uses leading wildcard LIKE'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |
    CREATE TABLE t (id INT);
    SELECT * FROM t WHERE a LIKE 'abc%';
  changeType: 1
- statement: |
    CREATE TABLE t (id INT);
    SELECT * FROM t WHERE a LIKE '%abc';
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: '"SELECT * FROM t WHERE a LIKE ''%abc'';" uses leading wildcard LIKE'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE t (id INT);
    SELECT * FROM t WHERE a LIKE 'abc' OR a LIKE '%abc';
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: '"SELECT * FROM t WHERE a LIKE ''abc'' OR a LIKE ''%abc'';" uses leading wildcard LIKE'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE t (id INT, a VARCHAR(255));
    SELECT *
    FROM t
    WHERE a LIKE '%acc'
      OR a LIKE '%abc';
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: |-
        "SELECT *
        FROM t
        WHERE a LIKE '%acc'
          OR a LIKE '%abc';" uses leading wildcard LIKE
      startposition:
        line: 3
        column: 0
      endposition: null
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: |-
        "SELECT *
        FROM t
        WHERE a LIKE '%acc'
          OR a LIKE '%abc';" uses leading wildcard LIKE
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: |
    CREATE TABLE t (id INT, a VARCHAR(255));
    SELECT * FROM
      (
      SELECT * FROM t
      WHERE a LIKE '%acc'
        OR a LIKE '%abc'
      ) t1;
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: |-
        "SELECT * FROM
          (
          SELECT * FROM t
          WHERE a LIKE '%acc'
            OR a LIKE '%abc'
          ) t1;" uses leading wildcard LIKE
      startposition:
        line: 4
        column: 0
      endposition: null
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: |-
        "SELECT * FROM
          (
          SELECT * FROM t
          WHERE a LIKE '%acc'
            OR a LIKE '%abc'
          ) t1;" uses leading wildcard LIKE
      startposition:
        line: 5
        column: 0
      endposition: null
