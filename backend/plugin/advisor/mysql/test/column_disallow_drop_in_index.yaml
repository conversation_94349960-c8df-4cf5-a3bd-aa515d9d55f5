- statement: |
    CREATE TABLE userTable(
      id INT NOT NULL,
      name VARCHAR(255) CHARSET ascii,
      roomId INT,
      time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      content BLOB NOT NULL COMMENT 'comment',
      json_content JSON NOT NULL COMMENT 'comment',
      INDEX idx1(name),
      UNIQUE KEY uk1(id, name),
      FOREIGN KEY fk1(roomId) REFERENCES room(id),
      INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
- statement: |
    CREATE TABLE user(
      id INT PRIMARY KEY COMMENT 'comment',
      name VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'comment',
      room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',
      updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      INDEX idx_user_name(name),
      UNIQUE KEY uk_user_id_name(id, name)
    ) ENGINE = INNODB COMMENT 'comment';
  changeType: 1
- statement: |-
    CREATE TABLE t(a int, b int, INDEX idx_a(a));
    ALTER TABLE t DROP COLUMN b;
  changeType: 1
- statement: |-
    CREATE TABLE t(a int, b int, INDEX idx_b(b));
    ALTER TABLE t DROP COLUMN b;
  changeType: 1
  want:
    - status: 2
      code: 424
      title: column.disallow-drop-in-index
      content: '`t`.`b` cannot drop index column'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, c int, INDEX idx_ac(a,c));
    ALTER TABLE t DROP COLUMN c;
  changeType: 1
  want:
    - status: 2
      code: 424
      title: column.disallow-drop-in-index
      content: '`t`.`c` cannot drop index column'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int, INDEX idx_a(a));
    CREATE TABLE y(a int, b int);
    ALTER TABLE y DROP COLUMN a;
  changeType: 1
- statement: |-
    CREATE TABLE t(a int,b int, c int, INDEX idx_ac(a,c));
    ALTER TABLE t DROP COLUMN a;
    ALTER TABLE t DROP COLUMN c;
  changeType: 1
  want:
    - status: 2
      code: 424
      title: column.disallow-drop-in-index
      content: '`t`.`a` cannot drop index column'
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 424
      title: column.disallow-drop-in-index
      content: '`t`.`c` cannot drop index column'
      startposition:
        line: 2
        column: 0
      endposition: null
