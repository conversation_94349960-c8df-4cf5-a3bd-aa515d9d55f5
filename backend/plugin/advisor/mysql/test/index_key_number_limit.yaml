- statement: CREATE TABLE t(a int, b int, primary key (a, b));
  changeType: 1
- statement: |-
    CREATE TABLE t(
      a int,
      b int,
      primary key pk (a, b, b, b, b, b, b),
      unique key uk (a, b, b, b, b, b, b),
      foreign key fk (a, b, b, b, b, b) references t1(a, b, b, b, b, b)
    );
  changeType: 1
  want:
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `pk` in table `t` should be not greater than 5
      startposition:
        line: 3
        column: 0
      endposition: null
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `uk` in table `t` should be not greater than 5
      startposition:
        line: 4
        column: 0
      endposition: null
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `fk` in table `t` should be not greater than 5
      startposition:
        line: 5
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int, c int, d int, e int, f int, g int);
    CREATE INDEX idx ON t(a, b, c, d, e, f, g);
    CREATE UNIQUE INDEX uk ON t(a, b, c, d, e, f, g);
  changeType: 1
  want:
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `idx` in table `t` should be not greater than 5
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `uk` in table `t` should be not greater than 5
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int, c int, d int, e int, f int, g int);
    ALTER TABLE t ADD PRIMARY KEY pk (a, b, c, d, e, f, g);
    ALTER TABLE t ADD UNIQUE KEY uk (a, b, c, d, e, f, g);
  changeType: 1
  want:
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `pk` in table `t` should be not greater than 5
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `uk` in table `t` should be not greater than 5
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int, c int, d int, e int, f int, g int);
    ALTER TABLE t ADD FOREIGN KEY uk (a, b, c, d, e, f, g) REFERENCES t1(a, b, c, d, e, f, g)
  changeType: 1
  want:
    - status: 2
      code: 802
      title: index.key-number-limit
      content: The number of index `uk` in table `t` should be not greater than 5
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b varchar(255));
    CREATE FULLTEXT INDEX idx ON t(b, b, b, b, b, b);
  changeType: 1
