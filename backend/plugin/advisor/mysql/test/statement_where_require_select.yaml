- statement: |
    CREATE TABLE book(id INT);
    INSERT INTO book(id) values (1);
  changeType: 1
- statement: |
    CREATE TABLE book(id INT);
    SELECT id FROM book;
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.select
      content: '"SELECT id FROM book;" requires WHERE clause'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(id INT);
    SELECT id FROM book WHERE id > 0;
  changeType: 1
- statement: |
    CREATE TABLE book(id INT);
    SELECT id
    FROM book
    WHERE id > (
      SELECT max(id)
      FROM book
      );
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.select
      content: |-
        "SELECT id
        FROM book
        WHERE id > (
          SELECT max(id)
          FROM book
          );" requires WHERE clause
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: |
    SELECT CURDATE();
  changeType: 1
