- statement: |-
    CREATE TABLE book(
      id int PRIMARY KEY DEFAULT 0
    );
  changeType: 1
- statement: |-
    CREATE TABLE book(
      id int PRIMARY KEY
    );
  changeType: 1
- statement: |-
    CREATE TABLE book(
      id int NOT NULL
    );
  changeType: 1
  want:
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`id` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE book(
      id int,
      PRIMARY KEY (id)
    );
  changeType: 1
- statement: |-
    CREATE TABLE book(a int);
    ALTER TABLE book ADD COLUMN id int PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE book(a int);
    ALTER TABLE book ADD COLUMN id int NOT NULL;
  changeType: 1
  want:
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`id` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE book(a int);
    ALTER TABLE book ADD COLUMN (b int NOT NULL, c int NOT NULL);
  changeType: 1
  want:
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`b` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`c` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE book(id int);
    ALTER TABLE book MODIFY COLUMN id int NOT NULL;
  changeType: 1
  want:
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`id` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE book(id int);
    ALTER TABLE book MODIFY COLUMN id int PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE book(uid int);
    ALTER TABLE book CHANGE COLUMN uid id int PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE book(uid int);
    ALTER TABLE book CHANGE COLUMN uid id int NOT NULL;
  changeType: 1
  want:
    - status: 2
      code: 404
      title: column.set-default-for-not-null
      content: Column `book`.`id` is NOT NULL but doesn't have DEFAULT
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE book(uid int, id int);
    ALTER TABLE book CHANGE COLUMN uid uid int NOT NULL DEFAULT 0, MODIFY COLUMN id int PRIMARY KEY DEFAULT 0, ADD COLUMN name varchar(20) NOT NULL DEFAULT '';
  changeType: 1
- statement: |-
    create table all_types(
      c1 tinyblob not null,
      c2 blob not null,
      c3 mediumblob not null,
      c4 longblob not null,
      c5 tinytext not null,
      c6 text not null,
      c7 mediumtext not null,
      c8 longtext not null,
      c9 json not null,
      c10 serial not null,
      c11 long varbinary not null,
      c12 long varchar not null
    );
  changeType: 1
