- statement: CREATE TABLE test(id INT PRIMARY KEY COMMENT 'comment',name VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'comment',room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',INDEX idx_test_name(name),UNIQUE KEY uk_test_id_name(id, name)) ENGINE = INNODB COMMENT 'comment'
  changeType: 1
- statement: |
    CREATE TABLE userTable(
      id INT NOT NULL,
      name VARCHAR(255) CHARSET ascii,
      roomId INT,
      time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      content BLOB NOT NULL COMMENT 'comment',
      json_content JSON NOT NULL COMMENT 'comment',
      INDEX idx1(name),
      UNIQUE KEY uk1(id, name),
      FOREIGN KEY fk1(roomId) REFERENCES room(id),
      INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: |-
        "CREATE TABLE userTable(
          id INT NOT NULL,
          name VARCHAR(255) CHARSET ascii,
          roomId INT,
          time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
          time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
          content BLOB NOT NULL COMMENT 'comment',
          json_content JSON NOT NULL COMMENT 'comment',
          INDEX idx1(name),
          UNIQUE KEY uk1(id, name),
          FOREIGN KEY fk1(roomId) REFERENCES room(id),
          INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;" used disabled collation 'latin1_bin'
      startposition:
        line: 11
        column: 0
      endposition: null
- statement: |
    CREATE TABLE user(
      id INT PRIMARY KEY COMMENT 'comment',
      name VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'comment',
      room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',
      updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      INDEX idx_user_name(name),
      UNIQUE KEY uk_user_id_name(id, name)
    ) ENGINE = INNODB COMMENT 'comment';
  changeType: 1
- statement: CREATE TABLE t(a int) COLLATE utf8mb4_polish_ci;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"CREATE TABLE t(a int) COLLATE utf8mb4_polish_ci;" used disabled collation ''utf8mb4_polish_ci'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(a varchar(255));
  changeType: 1
- statement: CREATE TABLE t(a int) COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"CREATE TABLE t(a int) COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"ALTER TABLE t COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: ALTER DATABASE test COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"ALTER DATABASE test COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin);
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"CREATE TABLE t(a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin);" used disabled collation ''latin1_bin'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"ALTER TABLE t ADD COLUMN a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t MODIFY COLUMN a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"ALTER TABLE t MODIFY COLUMN a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t CHANGE COLUMN a a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 1201
      title: system.collation.allowlist
      content: '"ALTER TABLE t CHANGE COLUMN a a varchar(255) CHARACTER SET latin1 COLLATE latin1_bin;" used disabled collation ''latin1_bin'''
      startposition:
        line: 1
        column: 0
      endposition: null
