- statement: |-
    CREATE TABLE t1(a INT AUTO_INCREMENT);
    CREATE TABLE t2(a int AUTO_INCREMENT);
    CREATE TABLE t3(a INTEGER AUTO_INCREMENT);
    CREATE TABLE t4(a integer AUTO_INCREMENT);
    CREATE TABLE t5(a TINYINT AUTO_INCREMENT);
    CREATE TABLE t6(a tinyint AUTO_INCREMENT);
    CREATE TABLE t7(a mediumint AUTO_INCREMENT);
    CREATE TABLE t8(a MEDIUMINT AUTO_INCREMENT);
    CREATE TABLE t9(a bigint AUTO_INCREMENT);
    CREATE TABLE t10(a BIGINT AUTO_INCREMENT);
  changeType: 1
- statement: |-
    CREATE TABLE t1(a INT AUTO_INCREMENT);
    CREATE TABLE t2(a varchar(255) AUTO_INCREMENT);
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t2`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN a varchar(255) AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN a varchar(255) AUTO_INCREMENT, ADD COLUMN c INT;
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN (a varchar(255) AUTO_INCREMENT, c INT);
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int);
    ALTER TABLE t MODIFY COLUMN a varchar(255) AUTO_INCREMENT;
    ALTER TABLE t MODIFY COLUMN b varchar(255);
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t CHANGE COLUMN b a varchar(255) AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 410
      title: column.auto-increment-must-integer
      content: Auto-increment column `t`.`a` requires integer type
      startposition:
        line: 1
        column: 0
      endposition: null
