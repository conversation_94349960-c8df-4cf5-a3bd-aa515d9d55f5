- statement: ALTER TABLE tech_book CHANGE name f2 TEXT;
  changeType: 1
  want:
    - status: 2
      code: 111
      title: schema.backward-compatibility
      content: '"ALTER TABLE tech_book CHANGE name f2 TEXT;" may cause incompatibility with the existing data and code'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name TEXT;
  changeType: 1
  want:
    - status: 2
      code: 111
      title: schema.backward-compatibility
      content: '"ALTER TABLE tech_book MODIFY name TEXT;" may cause incompatibility with the existing data and code'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name TEXT NULL;
  changeType: 1
  want:
    - status: 2
      code: 111
      title: schema.backward-compatibility
      content: '"ALTER TABLE tech_book MODIFY name TEXT NULL;" may cause incompatibility with the existing data and code'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name TEXT NOT NULL;
  changeType: 1
  want:
    - status: 2
      code: 111
      title: schema.backward-compatibility
      content: '"ALTER TABLE tech_book MODIFY name TEXT NOT NULL;" may cause incompatibility with the existing data and code'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name TEXT COMMENT 'bla';
  changeType: 1
  want:
    - status: 2
      code: 111
      title: schema.backward-compatibility
      content: '"ALTER TABLE tech_book MODIFY name TEXT COMMENT ''bla'';" may cause incompatibility with the existing data and code'
      startposition:
        line: 0
        column: 0
      endposition: null
