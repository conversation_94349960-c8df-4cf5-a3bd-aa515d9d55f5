- statement: |-
    CREATE TABLE employee (
      id INT PRIMARY KEY,
      name <PERSON><PERSON><PERSON><PERSON>(50)
    ) CHARSET=utf8;
  changeType: 1
  want:
    - status: 2
      code: 612
      title: table.disallow-set-charset
      content: |-
        Set charset on tables is disallowed, but "CREATE TABLE employee (
          id INT PRIMARY KEY,
          name VARCHAR(50)
        ) CHARSET=utf8;" uses
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE employee (
      id INT PRIMARY KEY,
      name VA<PERSON>HA<PERSON>(50)
    );
    ALTER TABLE employee CONVERT TO CHARACTER SET utf8;
  changeType: 1
  want:
    - status: 2
      code: 612
      title: table.disallow-set-charset
      content: Set charset on tables is disallowed, but "ALTER TABLE employee CONVERT TO CHARACTER SET utf8;" uses
      startposition:
        line: 4
        column: 0
      endposition: null
