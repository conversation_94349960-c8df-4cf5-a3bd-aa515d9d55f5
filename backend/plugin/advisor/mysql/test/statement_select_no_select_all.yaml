- statement: SELECT * FROM t;
  changeType: 1
  want:
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: '"SELECT * FROM t;" uses SELECT all'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT a, b FROM t;
  changeType: 1
- statement: |
    SELECT a, b FROM t;
    SELECT * FROM t;
    SELECT a, b FROM (SELECT * from t1 JOIN t2) t;
  changeType: 1
  want:
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: '"SELECT * FROM t;" uses SELECT all'
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: '"SELECT a, b FROM (SELECT * from t1 JOIN t2) t;" uses SELECT all'
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    SELECT a, b FROM t;
    SELECT * FROM t1
    UNION
    SELECT * FROM t2;
  changeType: 1
  want:
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: |-
        "SELECT * FROM t1
        UNION
        SELECT * FROM t2;" uses SELECT all
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: |-
        "SELECT * FROM t1
        UNION
        SELECT * FROM t2;" uses SELECT all
      startposition:
        line: 3
        column: 0
      endposition: null
- statement: |-
    SELECT a, b FROM t;
    WITH cte AS (
      SELECT * FROM a
    ) SELECT * FROM cte;
  changeType: 1
  want:
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: |-
        "WITH cte AS (
          SELECT * FROM a
        ) SELECT * FROM cte;" uses SELECT all
      startposition:
        line: 2
        column: 0
      endposition: null
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: |-
        "WITH cte AS (
          SELECT * FROM a
        ) SELECT * FROM cte;" uses SELECT all
      startposition:
        line: 3
        column: 0
      endposition: null
- statement: |-
    SELECT a, b FROM t;
    SELECT *
    FROM a
    INNER JOIN b USING(id);
  changeType: 1
  want:
    - status: 2
      code: 203
      title: statement.select.no-select-all
      content: |-
        "SELECT *
        FROM a
        INNER JOIN b USING(id);" uses SELECT all
      startposition:
        line: 1
        column: 0
      endposition: null
