- statement: CREATE TABLE techBook(id int, name varchar(255));
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '`techBook` mismatches table naming convention, naming format should be "^[a-z]+(_[a-z]+)*$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE tech_book_copy(id int, name varchar(255));
  changeType: 1
- statement: CREATE TABLE cjnubexocfhqoogdmihudyahmmghviqkzvpixnwvxtxumvuannpwdcbtsgwrvzpde(id int, name varchar(255));
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '`cjnubexocfhqoogdmihudyahmmghviqkzvpixnwvxtxumvuannpwdcbtsgwrvzpde` mismatches table naming convention, its length should be within 64 characters'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(a INT);
    ALTER TABLE book RENAME TO TechBook;
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '`TechBook` mismatches table naming convention, naming format should be "^[a-z]+(_[a-z]+)*$"'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(a INT);
    ALTER TABLE book RENAME TO tech_book_copy;
  changeType: 1
- statement: |
    CREATE TABLE book (a INT);
    RENAME TABLE book TO tech_book_copy, tech_book_copy TO LiteraryBook;
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '`LiteraryBook` mismatches table naming convention, naming format should be "^[a-z]+(_[a-z]+)*$"'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE literary_book(a int);
    CREATE TABLE book(a int);
    RENAME TABLE book TO TechBook, literary_book TO LiteraryBook;
  changeType: 1
  want:
    - status: 2
      code: 301
      title: naming.table
      content: '`LiteraryBook` mismatches table naming convention, naming format should be "^[a-z]+(_[a-z]+)*$"'
      startposition:
        line: 2
        column: 0
      endposition: null
    - status: 2
      code: 301
      title: naming.table
      content: '`TechBook` mismatches table naming convention, naming format should be "^[a-z]+(_[a-z]+)*$"'
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |
    CREATE TABLE literary_book(a int);
    CREATE TABLE book(a int);
    RENAME TABLE book TO tech_book_copy, literary_book TO literary_book_copy;
  changeType: 1
