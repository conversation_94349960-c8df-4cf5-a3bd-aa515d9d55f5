- statement: CREATE TABLE t1(name char);
  changeType: 1
- statement: CREATE TABLE t(name char(20));
  changeType: 1
- statement: CREATE TABLE t(name varchar(225));
  changeType: 1
- statement: |-
    CREATE TABLE t1(name char(20));
    CREATE TABLE t2(name char(225));
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The length of the CHAR column `t2.name` is bigger than 20, please use VARCHAR instead
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN name_1 char(20);
    ALTER TABLE tech_book ADD COLUMN name_2 char(225);
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The length of the CHAR column `tech_book.name_2` is bigger than 20, please use VARCHAR instead
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book CHANGE COLUMN name name char(20);
    ALTER TABLE tech_book CHANGE COLUMN name name char(225);
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The length of the CHAR column `tech_book.name` is bigger than 20, please use VARCHAR instead
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book MODIFY COLUMN name char(20);
    ALTER TABLE tech_book MODIFY COLUMN name char(225);
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The length of the CHAR column `tech_book.name` is bigger than 20, please use VARCHAR instead
      startposition:
        line: 1
        column: 0
      endposition: null
