- statement: CREATE TABLE t(a int);
  changeType: 1
- statement: |-
    CREATE TABLE b(a INT);
    CREATE TABLE t(a int) PARTITION BY RANGE (a) (
            PARTITION p0 VALUES LESS THAN (6),
            PARTITION p1 VALUES LESS THAN (11)
          );
  changeType: 1
  want:
    - status: 2
      code: 608
      title: table.disallow-partition
      content: |-
        Table partition is forbidden, but "CREATE TABLE t(a int) PARTITION BY RANGE (a) (
                PARTITION p0 VALUES LESS THAN (6),
                PARTITION p1 VALUES LESS THAN (11)
              );" creates
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book PARTITION BY RANGE (a) (
            PARTITION p0 VALUES LESS THAN (6),
            PARTITION p1 VALUES LESS THAN (11)
          );
  changeType: 1
  want:
    - status: 2
      code: 608
      title: table.disallow-partition
      content: |-
        Table partition is forbidden, but "ALTER TABLE tech_book PARTITION BY RANGE (a) (
                PARTITION p0 VALUES LESS THAN (6),
                PARTITION p1 VALUES LESS THAN (11)
              );" creates
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int) PARTITION BY RANGE (a) (
            PARTITION p0 VALUES LESS THAN (6),
            PARTITION p1 VALUES LESS THAN (11)
          );
    ALTER TABLE tech_book PARTITION BY RANGE (a) (
            PARTITION p0 VALUES LESS THAN (6),
            PARTITION p1 VALUES LESS THAN (11)
          );
  changeType: 1
  want:
    - status: 2
      code: 608
      title: table.disallow-partition
      content: |-
        Table partition is forbidden, but "CREATE TABLE t(a int) PARTITION BY RANGE (a) (
                PARTITION p0 VALUES LESS THAN (6),
                PARTITION p1 VALUES LESS THAN (11)
              );" creates
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 608
      title: table.disallow-partition
      content: |-
        Table partition is forbidden, but "ALTER TABLE tech_book PARTITION BY RANGE (a) (
                PARTITION p0 VALUES LESS THAN (6),
                PARTITION p1 VALUES LESS THAN (11)
              );" creates
      startposition:
        line: 4
        column: 0
      endposition: null
