- statement: |
    CREATE TABLE userTable(
      id INT NOT NULL,
      name VARCHA<PERSON>(255) CHARSET ascii,
      roomId INT,
      time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      content BLOB NOT NULL COMMENT 'comment',
      json_content JSON NOT NULL COMMENT 'comment',
      INDEX idx1(name),
      UNIQUE KEY uk1(id, name),
      FOREIGN KEY fk1(roomId) REFERENCES room(id),
      INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `userTable` requires PRIMARY KEY
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |
    CREATE TABLE user(
      id INT PRIMARY KEY COMMENT 'comment',
      name VARCHA<PERSON>(255) NOT NULL DEFAULT '' COMMENT 'comment',
      room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',
      updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      INDEX idx_user_name(name),
      UNIQUE KEY uk_user_id_name(id, name)
    ) ENGINE = INNODB COMMENT 'comment';
  changeType: 1
- statement: |
    CREATE TABLE t1(id INT PRIMARY KEY);
    CREATE TABLE t2(id INT);
    ALTER TABLE t2 ADD UNIQUE INDEX uk (id);
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t2` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t1(id INT, PRIMARY KEY (id));
    CREATE TABLE t2(id INT);
    ALTER TABLE t2 ADD COLUMN uid INT UNIQUE KEY;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t2` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t1(id INT PRIMARY KEY);
    CREATE TABLE t2(id INT, PRIMARY KEY (id));
  changeType: 1
- statement: |-
    CREATE TABLE t1(id INT PRIMARY KEY);
    CREATE TABLE t2(id INT);
    DROP TABLE t1;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t2` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t1(id INT PRIMARY KEY);
    CREATE TABLE t2(id INT);
    DROP TABLE t1;
    DROP TABLE t2;
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT);
    ALTER TABLE t ADD CONSTRAINT PRIMARY KEY (id);
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT PRIMARY KEY);
    ALTER TABLE t DROP PRIMARY KEY;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id INT PRIMARY KEY);
    ALTER TABLE t DROP INDEX `PRIMARY`;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id INT);
    ALTER TABLE t ADD COLUMN name varchar(30) PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT);
    ALTER TABLE t CHANGE COLUMN id id INT PRIMARY KEY;
  changeType: 1
- statement: ALTER TABLE tech_book CHANGE COLUMN id uid INT;
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT);
    ALTER TABLE t MODIFY COLUMN id INT PRIMARY KEY;
  changeType: 1
- statement: ALTER TABLE tech_book MODIFY COLUMN id INT;
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT, name varchar(30), PRIMARY KEY(id, name));
    ALTER TABLE t DROP COLUMN id;
  changeType: 1
- statement: |-
    CREATE TABLE t(id INT, name varchar(30), comment varchar(255), PRIMARY KEY(id, name));
    ALTER TABLE t ADD COLUMN age INT;
    ALTER TABLE t DROP COLUMN id, DROP COLUMN name;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `t` requires PRIMARY KEY
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN c INT;
    ALTER TABLE tech_book ADD COLUMN a int, DROP COLUMN id, DROP COLUMN name;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `tech_book` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book DROP COLUMN name;
  changeType: 1
- statement: |-
    ALTER TABLE tech_book ADD COLUMN c INT;
    ALTER TABLE tech_book DROP COLUMN name, DROP COLUMN id;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `tech_book` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book CHANGE COLUMN id uid int;
    ALTER TABLE tech_book ADD COLUMN a int, DROP COLUMN uid, DROP COLUMN name;
  changeType: 1
  want:
    - status: 2
      code: 601
      title: table.require-pk
      content: Table `tech_book` requires PRIMARY KEY
      startposition:
        line: 1
        column: 0
      endposition: null
