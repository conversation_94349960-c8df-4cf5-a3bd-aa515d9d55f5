- statement: CREATE TABLE t(a int) CHARSET utf8mb4;
  changeType: 1
- statement: CREATE TABLE t(a varchar(255));
  changeType: 1
- statement: CREATE TABLE t(a int) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"CREATE TABLE t(a int) CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"ALTER TABLE t CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: ALTER DATABASE test CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"ALTER DATABASE test CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(a varchar(255) CHARSET ascii);
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"CREATE TABLE t(a varchar(255) CHARSET ascii);" used disabled charset ''ascii'''
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN a varchar(255) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"ALTER TABLE t ADD COLUMN a varchar(255) CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t MODIFY COLUMN a varchar(255) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"ALTER TABLE t MODIFY COLUMN a varchar(255) CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t CHANGE COLUMN a a varchar(255) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 1001
      title: system.charset.allowlist
      content: '"ALTER TABLE t CHANGE COLUMN a a varchar(255) CHARSET ascii;" used disabled charset ''ascii'''
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: /* this is a comment */
  changeType: 1
