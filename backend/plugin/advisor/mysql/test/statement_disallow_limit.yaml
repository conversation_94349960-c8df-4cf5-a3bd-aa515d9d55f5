- statement: INSERT INTO tech_book SELECT * FROM tech_book;
  changeType: 1
- statement: |
    INSERT INTO tech_book SELECT * FROM tech_book;
    INSERT INTO tech_book SELECT * FROM tech_book LIMIT 1;
  changeType: 1
  want:
    - status: 2
      code: 1103
      title: statement.disallow-limit
      content: LIMIT clause is forbidden in INSERT, UPDATE and DELETE statement, but "INSERT INTO tech_book SELECT * FROM tech_book LIMIT 1;" uses
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    INSERT INTO tech_book SELECT * FROM tech_book;
    INSERT INTO tech_book SELECT * FROM tech_book UNION SELECT * FROM tech_book LIMIT 1;
  changeType: 1
  want:
    - status: 2
      code: 1103
      title: statement.disallow-limit
      content: LIMIT clause is forbidden in INSERT, UPDATE and DELETE statement, but "INSERT INTO tech_book SELECT * FROM tech_book UNION SELECT * FROM tech_book LIMIT 1;" uses
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    INSERT INTO tech_book SELECT * FROM tech_book;
    INSERT INTO tech_book SELECT * FROM tech_book UNION SELECT * FROM tech_book LIMIT 1;
  changeType: 1
  want:
    - status: 2
      code: 1103
      title: statement.disallow-limit
      content: LIMIT clause is forbidden in INSERT, UPDATE and DELETE statement, but "INSERT INTO tech_book SELECT * FROM tech_book UNION SELECT * FROM tech_book LIMIT 1;" uses
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    UPDATE tech_book SET name = 'my name';
    UPDATE tech_book SET name = 'my name' LIMIT 10;
  changeType: 1
  want:
    - status: 2
      code: 1102
      title: statement.disallow-limit
      content: LIMIT clause is forbidden in INSERT, UPDATE and DELETE statement, but "UPDATE tech_book SET name = 'my name' LIMIT 10;" uses
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    DELETE FROM tech_book;
    DELETE FROM tech_book LIMIT 10;
  changeType: 1
  want:
    - status: 2
      code: 1106
      title: statement.disallow-limit
      content: LIMIT clause is forbidden in INSERT, UPDATE and DELETE statement, but "DELETE FROM tech_book LIMIT 10;" uses
      startposition:
        line: 1
        column: 0
      endposition: null
