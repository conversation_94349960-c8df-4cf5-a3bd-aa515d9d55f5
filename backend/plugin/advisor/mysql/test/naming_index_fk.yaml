- statement: CREATE TABLE userTable(id INT NOT NULL,name <PERSON><PERSON><PERSON><PERSON>(255) CHARSET ascii,roomId INT,time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',content BLOB NOT NULL COMMENT 'comment',json_content JSON NOT NULL COMMENT 'comment',INDEX idx1(name),UNIQUE KEY uk1(id, name),FOREIGN KEY fk1(roomId) REFERENCES room(id),INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 305
      title: naming.index.fk
      content: Foreign key in table `userTable` mismatches the naming convention, expect "^$|^fk_userTable_roomId_room_id$" but found `fk1`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ADD CONSTRAINT fk_tech_book_author_id_author_id FOREIGN <PERSON>EY (author_id) REFERENCES author (id);
  changeType: 1
- statement: ALTER TABLE tech_book ADD CONSTRAINT fk_author_id FOREIGN KEY (author_id) REFERENCES author (id);
  changeType: 1
  want:
    - status: 2
      code: 305
      title: naming.index.fk
      content: Foreign key in table `tech_book` mismatches the naming convention, expect "^$|^fk_tech_book_author_id_author_id$" but found `fk_author_id`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ADD CONSTRAINT rvemempmcmmhutaskvcidmwaldtfjdgmcpkhmibtadfydtkexpuzczrrneucthfwk FOREIGN KEY (author_id) REFERENCES author (id);
  changeType: 1
  want:
    - status: 2
      code: 305
      title: naming.index.fk
      content: Foreign key `rvemempmcmmhutaskvcidmwaldtfjdgmcpkhmibtadfydtkexpuzczrrneucthfwk` in table `tech_book` mismatches the naming convention, its length should be within 64 characters
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 305
      title: naming.index.fk
      content: Foreign key in table `tech_book` mismatches the naming convention, expect "^$|^fk_tech_book_author_id_author_id$" but found `rvemempmcmmhutaskvcidmwaldtfjdgmcpkhmibtadfydtkexpuzczrrneucthfwk`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(
      id INT,
      author_id INT,
      FOREIGN KEY  fk_book_author_id_author_id (author_id) REFERENCES author (id)
    );
  changeType: 1
- statement: |
    CREATE TABLE book(
      id INT,
      author_id INT,
      FOREIGN KEY (author_id) REFERENCES author (id)
    );
  changeType: 1
- statement: |
    CREATE TABLE book(
      id INT,
      author_id INT,
      FOREIGN KEY fk_book_author_id (author_id) REFERENCES author (id)
    );
  changeType: 1
  want:
    - status: 2
      code: 305
      title: naming.index.fk
      content: Foreign key in table `book` mismatches the naming convention, expect "^$|^fk_book_author_id_author_id$" but found `fk_book_author_id`
      startposition:
        line: 3
        column: 0
      endposition: null
