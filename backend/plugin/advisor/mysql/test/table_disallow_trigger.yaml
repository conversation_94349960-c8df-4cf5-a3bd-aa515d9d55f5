- statement: CREATE TABLE t(a int);
  changeType: 1
- statement: |-
    CREATE TABLE account (acct_num INT, amount DECIMAL(10,2));
    CREATE TRIGGER ins_sum BEFORE INSERT ON account
       FOR EACH ROW SET @sum = @sum + NEW.amount;
  changeType: 1
  want:
    - status: 2
      code: 610
      title: table.disallow-trigger
      content: |-
        Trigger is forbidden, but "CREATE TRIGGER ins_sum BEFORE INSERT ON account
           FOR EACH ROW SET @sum = @sum + NEW.amount;" creates
      startposition:
        line: 1
        column: 0
      endposition: null
