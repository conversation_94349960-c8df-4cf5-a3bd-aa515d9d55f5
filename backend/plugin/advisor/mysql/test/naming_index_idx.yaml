- statement: CREATE INDEX idx_tech_book_id_name ON tech_book(id, name);
  changeType: 1
- statement: CREATE INDEX tech_book_id_name ON tech_book(id, name);
  changeType: 1
  want:
    - status: 2
      code: 303
      title: naming.index.idx
      content: Index in table `tech_book` mismatches the naming convention, expect "^$|^idx_tech_book_id_name$" but found `tech_book_id_name`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE INDEX afvjwsgrbgqzjfrkmbcoxzstznuypasijbbcdykoboredqovetzfcmmqliaelyavw ON tech_book(id, name);
  changeType: 1
  want:
    - status: 2
      code: 303
      title: naming.index.idx
      content: Index `afvjwsgrbgqzjfrkmbcoxzstznuypasijbbcdykoboredqovetzfcmmqliaelyavw` in table `tech_book` mismatches the naming convention, its length should be within 64 characters
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 303
      title: naming.index.idx
      content: Index in table `tech_book` mismatches the naming convention, expect "^$|^idx_tech_book_id_name$" but found `afvjwsgrbgqzjfrkmbcoxzstznuypasijbbcdykoboredqovetzfcmmqliaelyavw`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book RENAME INDEX old_index TO idx_tech_book_id_name;
  changeType: 1
- statement: ALTER TABLE tech_book RENAME INDEX old_index TO idx_tech_book;
  changeType: 1
  want:
    - status: 2
      code: 303
      title: naming.index.idx
      content: Index in table `tech_book` mismatches the naming convention, expect "^$|^idx_tech_book_id_name$" but found `idx_tech_book`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ADD INDEX idx_tech_book_id_name (id, name);
  changeType: 1
- statement: ALTER TABLE tech_book ADD INDEX tech_book_id_name (id, name);
  changeType: 1
  want:
    - status: 2
      code: 303
      title: naming.index.idx
      content: Index in table `tech_book` mismatches the naming convention, expect "^$|^idx_tech_book_id_name$" but found `tech_book_id_name`
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE tech_book_copy(id INT PRIMARY KEY, name VARCHAR(20), INDEX idx_tech_book_copy_name (name));
  changeType: 1
- statement: CREATE TABLE tech_book_copy(id INT PRIMARY KEY, name VARCHAR(20), INDEX (name));
  changeType: 1
- statement: CREATE TABLE tech_book_copy(id INT PRIMARY KEY, name VARCHAR(20), UNIQUE INDEX (name));
  changeType: 1
- statement: CREATE TABLE tech_book_copy(id INT PRIMARY KEY, name VARCHAR(20), FULLTEXT INDEX (name));
  changeType: 1
- statement: CREATE TABLE tech_book_copy(id INT PRIMARY KEY, name VARCHAR(20), SPATIAL INDEX (name));
  changeType: 1
