- statement: |-
    CREATE TABLE dt_relation_temp(objto int);
    ALTER TABLE dt_relation_temp ADD COLUMN infos json null comment'关系信息点'AFTER`objto`;
  changeType: 1
- statement: CREATE TABLE t(a varchar(20));
  changeType: 1
- statement: |-
    CREATE TABLE t1(a varchar(20) CHARSET ascii);
    CREATE TABLE t2(a varchar(20) CHARSET ascii);
  changeType: 1
  want:
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "CREATE TABLE t1(a varchar(20) CHARSET ascii);" does
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "CREATE TABLE t2(a varchar(20) CHARSET ascii);" does
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN a varchar(20);
    ALTER TABLE tech_book ADD COLUMN b varchar(20) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book ADD COLUMN b varchar(20) CHARSET ascii;" does
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN a varchar(20), ADD COLUMN b varchar(20);
    ALTER TABLE tech_book ADD COLUMN (c varchar(20) CHARSET ascii, d varchar(20) CHARSET ascii);
    ALTER TABLE tech_book ADD COLUMN e varchar(20) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book ADD COLUMN (c varchar(20) CHARSET ascii, d varchar(20) CHARSET ascii);" does
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book ADD COLUMN (c varchar(20) CHARSET ascii, d varchar(20) CHARSET ascii);" does
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book ADD COLUMN e varchar(20) CHARSET ascii;" does
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book MODIFY COLUMN id int;
    ALTER TABLE tech_book MODIFY COLUMN id varchar(20) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book MODIFY COLUMN id varchar(20) CHARSET ascii;" does
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book CHANGE COLUMN name name int;
    ALTER TABLE tech_book CHANGE COLUMN name name varchar(20) CHARSET ascii;
  changeType: 1
  want:
    - status: 2
      code: 414
      title: column.disallow-set-charset
      content: Disallow set column charset but "ALTER TABLE tech_book CHANGE COLUMN name name varchar(20) CHARSET ascii;" does
      startposition:
        line: 1
        column: 0
      endposition: null
