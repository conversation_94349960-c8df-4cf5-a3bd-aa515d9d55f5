- statement: |
    CREATE TABLE userTable(
      id INT NOT NULL,
      name VARCHAR(255) CHARSET ascii,
      roomId INT,
      time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      content BLOB NOT NULL COMMENT 'comment',
      json_content JSON NOT NULL COMMENT 'comment',
      INDEX idx1(name),
      UNIQUE KEY uk1(id, name),
      FOREIGN KEY fk1(roomId) REFERENCES room(id),
      INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 501
      title: engine.mysql.use-innodb
      content: |-
        "CREATE TABLE userTable(
          id INT NOT NULL,
          name VARCHAR(255) CHARSET ascii,
          roomId INT,
          time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
          time_updated TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
          content BLOB NOT NULL COMMENT 'comment',
          json_content JSON NOT NULL COMMENT 'comment',
          INDEX idx1(name),
          UNIQUE KEY uk1(id, name),
          FOREIGN KEY fk1(roomId) REFERENCES room(id),
          INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;" doesn't use InnoDB engine
      startposition:
        line: 11
        column: 0
      endposition: null
- statement: |
    CREATE TABLE user(
      id INT PRIMARY KEY COMMENT 'comment',
      name VARCHAR(255) NOT NULL DEFAULT '' COMMENT 'comment',
      room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',
      updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      INDEX idx_user_name(name),
      UNIQUE KEY uk_user_id_name(id, name)
    ) ENGINE = INNODB COMMENT 'comment';
  changeType: 1
- statement: CREATE TABLE book(id int) ENGINE = INNODB;
  changeType: 1
- statement: CREATE TABLE book(id int);
  changeType: 1
- statement: |
    CREATE TABLE book(
      id INT,
      price INT
    ) ENGINE = CSV;
  changeType: 1
  want:
    - status: 2
      code: 501
      title: engine.mysql.use-innodb
      content: |-
        "CREATE TABLE book(
          id INT,
          price INT
        ) ENGINE = CSV;" doesn't use InnoDB engine
      startposition:
        line: 3
        column: 0
      endposition: null
- statement: |
    CREATE TABLE teck_book(a INT);
    CREATE TABLE book(
      id INT,
      price INT
    ) ENGINE = CSV;
  changeType: 1
  want:
    - status: 2
      code: 501
      title: engine.mysql.use-innodb
      content: |-
        "CREATE TABLE book(
          id INT,
          price INT
        ) ENGINE = CSV;" doesn't use InnoDB engine
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ENGINE = INNODB;
  changeType: 1
- statement: |
    CREATE TABLE teck_book(a INT);
    CREATE TABLE book(a INT);
    ALTER TABLE tech_book ENGINE = CSV;
  changeType: 1
  want:
    - status: 2
      code: 501
      title: engine.mysql.use-innodb
      content: '"ALTER TABLE tech_book ENGINE = CSV;" doesn''t use InnoDB engine'
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: SET default_storage_engine=INNODB;
  changeType: 1
- statement: |
    CREATE TABLE book(a INT);
    SET default_storage_engine=CSV;
  changeType: 1
  want:
    - status: 2
      code: 501
      title: engine.mysql.use-innodb
      content: '"SET default_storage_engine=CSV;" doesn''t use InnoDB engine'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    SET foreign_key_checks=0;
  changeType: 1
