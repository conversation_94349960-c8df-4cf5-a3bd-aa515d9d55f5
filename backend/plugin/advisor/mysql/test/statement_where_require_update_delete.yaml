- statement: |
    CREATE TABLE book(id INT);
    INSERT INTO book(id) values (1);
  changeType: 1
- statement: |
    CREATE TABLE book(id INT);
    DELETE FROM book;
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.update-delete
      content: '"DELETE FROM book;" requires WHERE clause'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(id INT);
    UPDATE book SET id = 1;
  changeType: 1
  want:
    - status: 2
      code: 202
      title: statement.where.require.update-delete
      content: '"UPDATE book SET id = 1;" requires WHERE clause'
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(id INT);
    DELETE FROM book WHERE id > 0;
  changeType: 1
- statement: |
    CREATE TABLE book(id INT);
    UPDATE book SET id = 1 WHERE id > 10;
  changeType: 1
