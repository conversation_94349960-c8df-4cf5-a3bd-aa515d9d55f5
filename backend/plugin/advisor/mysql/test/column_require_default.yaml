- statement: |-
    CREATE TABLE t(
      a bigint(20) unsigned not null,
      b varchar(50) not null unique,
      primary key (a)
    );
  changeType: 1
  want:
    - status: 2
      code: 420
      title: column.require-default
      content: Column `t`.`b` doesn't have DEFAULT.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(
      a int auto_increment primary key,
      b int default 1,
      c varchar(255) default 'default value',
      d blob,
      e json,
      f geometry
    );
  changeType: 1
- statement: |-
    CREATE TABLE t(
      a geometry,
      b point,
      c linestring,
      d polygon,
      e multipoint,
      f multilinestring,
      g multipolygon,
      h geometrycollection
    );
  changeType: 1
- statement: |-
    CREATE TABLE t1(
      a int primary key
    );
    CREATE TABLE t2(
      a int auto_increment primary key
    );
  changeType: 1
- statement: |-
    CREATE TABLE t(
      a int,
      b int default 1
    )
  changeType: 1
  want:
    - status: 2
      code: 420
      title: column.require-default
      content: Column `t`.`a` doesn't have DEFAULT.
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN a BLOB;
    ALTER TABLE tech_book ADD COLUMN b timestamp;
    ALTER TABLE tech_book MODIFY COLUMN a varchar(220);
  changeType: 1
  want:
    - status: 2
      code: 420
      title: column.require-default
      content: Column `tech_book`.`b` doesn't have DEFAULT.
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 420
      title: column.require-default
      content: Column `tech_book`.`a` doesn't have DEFAULT.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ADD COLUMN (a BLOB, b timestamp);
  changeType: 1
  want:
    - status: 2
      code: 420
      title: column.require-default
      content: Column `tech_book`.`b` doesn't have DEFAULT.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book ADD COLUMN a VARCHAR(255) DEFAULT 'default value';
    ALTER TABLE tech_book MODIFY COLUMN a char(25) DEFAULT 'default';
    ALTER TABLE tech_book CHANGE COLUMN a b int;
  changeType: 1
  want:
    - status: 2
      code: 420
      title: column.require-default
      content: Column `tech_book`.`b` doesn't have DEFAULT.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(
      id int,
      PRIMARY KEY (id)
    )
  changeType: 1
