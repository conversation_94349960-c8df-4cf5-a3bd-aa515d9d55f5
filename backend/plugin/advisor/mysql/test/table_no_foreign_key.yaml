- statement: |-
    CREATE TABLE book(id INT);
    ALTER TABLE tech_book ADD CONSTRAINT fk_tech_book_author_id_author_id FOREIGN KEY (author_id) REFERENCES author (id);
  changeType: 1
  want:
    - status: 2
      code: 602
      title: table.no-foreign-key
      content: Foreign key is not allowed in the table `tech_book`
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t1 (id INT);
    CREATE TABLE book(id INT, author_id INT, FOREIGN KEY fk_book_author_id_author_id (author_id) REFERENCES author (id));
  changeType: 1
  want:
    - status: 2
      code: 602
      title: table.no-foreign-key
      content: Foreign key is not allowed in the table `book`
      startposition:
        line: 1
        column: 0
      endposition: null
