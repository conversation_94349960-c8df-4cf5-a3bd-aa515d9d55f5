- statement: CREATE TABLE book(id int AUTO_INCREMENT);
  changeType: 1
- statement: CREATE TABLE book(a int AUTO_INCREMENT);
  changeType: 1
  want:
    - status: 2
      code: 307
      title: naming.column.auto-increment
      content: '`book`.`a` mismatches auto_increment column naming convention, naming format should be "^id$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book ADD COLUMN a int AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 307
      title: naming.column.auto-increment
      content: '`tech_book`.`a` mismatches auto_increment column naming convention, naming format should be "^id$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book CHANGE COLUMN id a int AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 307
      title: naming.column.auto-increment
      content: '`tech_book`.`a` mismatches auto_increment column naming convention, naming format should be "^id$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY COLUMN name int AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 307
      title: naming.column.auto-increment
      content: '`tech_book`.`name` mismatches auto_increment column naming convention, naming format should be "^id$"'
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |
    CREATE TABLE book(id int);
    ALTER TABLE tech_book MODIFY COLUMN id int AUTO_INCREMENT;
  changeType: 1
- statement: |
    CREATE TABLE book(name VARCHAR(255));
    ALTER TABLE book ADD COLUMN id int AUTO_INCREMENT;
  changeType: 1
- statement: |
    CREATE TABLE book(a INT, name VARCHAR(255));
    ALTER TABLE book CHANGE COLUMN a id int AUTO_INCREMENT;
  changeType: 1
- statement: |
    CREATE TABLE book(name VARCHAR(255));
    ALTER TABLE book ADD COLUMN (id int AUTO_INCREMENT, a int, b int, c int, d int);
  changeType: 1
