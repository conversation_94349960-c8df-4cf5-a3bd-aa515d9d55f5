- statement: CREATE TABLE t(a INT UNSIGNED AUTO_INCREMENT);
  changeType: 1
- statement: CREATE TABLE t(a INT AUTO_INCREMENT);
  changeType: 1
  want:
    - status: 2
      code: 417
      title: column.auto-increment-must-unsigned
      content: Auto-increment column `t`.`a` is not UNSIGNED type
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(a INT SIGNED AUTO_INCREMENT);
  changeType: 1
  want:
    - status: 2
      code: 417
      title: column.auto-increment-must-unsigned
      content: Auto-increment column `t`.`a` is not UNSIGNED type
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(a INT ZEROFILL AUTO_INCREMENT);
  changeType: 1
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t ADD COLUMN a INT AUTO_INCREMENT, ADD COLUMN c INT ZEROFILL AUTO_INCREMENT, ADD COLUMN d INT UNSIGNED AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 417
      title: column.auto-increment-must-unsigned
      content: Auto-increment column `t`.`a` is not UNSIGNED type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int, b int);
    ALTER TABLE t MODIFY COLUMN a INT AUTO_INCREMENT;
    ALTER TABLE t MODIFY COLUMN a INT UNSIGNED AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 417
      title: column.auto-increment-must-unsigned
      content: Auto-increment column `t`.`a` is not UNSIGNED type
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int);
    ALTER TABLE t CHANGE COLUMN b a INT AUTO_INCREMENT;
    ALTER TABLE t CHANGE COLUMN a c INT UNSIGNED AUTO_INCREMENT;
  changeType: 1
  want:
    - status: 2
      code: 417
      title: column.auto-increment-must-unsigned
      content: Auto-increment column `t`.`a` is not UNSIGNED type
      startposition:
        line: 1
        column: 0
      endposition: null
