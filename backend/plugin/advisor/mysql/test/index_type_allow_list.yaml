- statement: |-
    CREATE TABLE customers (
      id INT PRIMARY KEY,
      email VARCHAR(100),
      INDEX idx_email (email)
    );
  changeType: 1
- statement: |-
    CREATE TABLE orders (
        id INT PRIMARY KEY,
        order_number INT,
        customer VARCHAR(50)
    );
    CREATE INDEX idx_customer ON orders (customer, (order_number * 2));
    CREATE INDEX idx_customer_duplicate ON orders (customer, (order_number * 2));
  changeType: 1
- statement: |-
    CREATE TABLE locations (
      id INT AUTO_INCREMENT PRIMARY KEY,
      geom GEOMETRY NOT NULL
    );
    CREATE SPATIAL INDEX spatial_index_name ON locations(geom);
  changeType: 1
  want:
    - status: 2
      code: 816
      title: index.type-allow-list
      content: Index type `SPATIAL` is not allowed
      startposition:
        line: 4
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE locations (
      id INT AUTO_INCREMENT PRIMARY KEY,
      description TEXT NOT NULL
    );
    CREATE FULLTEXT INDEX fulltext_index_name ON locations(description);
  changeType: 1
  want:
    - status: 2
      code: 816
      title: index.type-allow-list
      content: Index type `FULLTEXT` is not allowed
      startposition:
        line: 4
        column: 0
      endposition: null
