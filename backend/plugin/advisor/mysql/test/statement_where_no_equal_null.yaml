- statement: |
    SELECT * FROM employee WHERE name = 'Jack' AND id IS NULL;
  changeType: 1
- statement: |
    UPDATE employee SET name = NULL;
  changeType: 1
- statement: |
    SELECT * FROM t WHERE name = 'test' AND id = NULL;
  changeType: 1
  want:
    - status: 2
      code: 221
      title: statement.where.no-equal-null
      content: 'WHERE clause contains equal null: SELECT * FROM t WHERE name = ''test'' AND id = NULL;'
      startposition:
        line: 0
        column: 0
      endposition: null
