- statement: CREATE TABLE t(id VARCHAR(5) PRIMARY KEY);
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(id INT PRIMARY KEY);
  changeType: 1
- statement: CREATE TABLE t(id VARCHAR(5), PRIMARY KEY(id));
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(id INT, id2 VARCHAR(5), id3 VARCHAR(5), PRIMARY KEY(id, id2, id3));
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id2` is varchar(5)
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id3` is varchar(5)
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(id INT, id2 BIGINT, PRIMARY KEY(id, id2));
  changeType: 1
- statement: CREATE TABLE t(id INT, PRIMARY KEY(id));
  changeType: 1
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id VARCHAR(5) PRIMARY KEY;
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id INT PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id VARCHAR(5), ADD PRIMARY KEY (id);
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id INT, ADD COLUMN id2 VARCHAR(5), ADD COLUMN id3 VARCHAR(5), ADD PRIMARY KEY (id, id2, id3);
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id2` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id3` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id INT, ADD COLUMN id2 BIGINT, ADD PRIMARY KEY (id, id2);
  changeType: 1
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN id BIGINT, ADD PRIMARY KEY (id);
  changeType: 1
- statement: |-
    CREATE TABLE t(id VARCHAR(5));
    ALTER TABLE t ADD PRIMARY KEY(id);
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id INT, id2 VARCHAR(5), id3 VARCHAR(5));
    ALTER TABLE t ADD PRIMARY KEY(id, id2, id3);
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id2` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id3` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id INT, id2 BIGINT);
    ALTER TABLE t ADD PRIMARY KEY(id, id2);
  changeType: 1
- statement: |-
    CREATE TABLE t(id BIGINT);
    ALTER TABLE t ADD PRIMARY KEY(id);
  changeType: 1
- statement: |-
    CREATE TABLE t(id int);
    ALTER TABLE t MODIFY COLUMN id VARCHAR(5) PRIMARY KEY;
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id int);
    ALTER TABLE t MODIFY COLUMN id INT(5) PRIMARY KEY;
  changeType: 1
- statement: |-
    CREATE TABLE t(id int);
    ALTER TABLE t CHANGE COLUMN id id2 VARCHAR(5) PRIMARY KEY;
  changeType: 1
  want:
    - status: 2
      code: 803
      title: index.pk-type-limit
      content: Columns in primary key must be INT/BIGINT but `t`.`id2` is varchar(5)
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(id int);
    ALTER TABLE t CHANGE COLUMN id id2 INT(5) PRIMARY KEY;
  changeType: 1
