- statement: |
    CREATE TABLE userTable(
      id INT NOT NULL,
      name VA<PERSON><PERSON><PERSON>(255) CHARSET ascii,
      roomId INT,
      time_created TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      time_updated TIMES<PERSON>MP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      content BLOB NOT NULL COMMENT 'comment',
      json_content JSON NOT NULL COMMENT 'comment',
      INDEX idx1(name),
      UNIQUE KEY uk1(id, name),
      FOREIGN KEY fk1(roomId) REFERENCES room(id),
      INDEX idx_userTable_content(content)) ENGINE = CSV COLLATE latin1_bin;
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `userTable`.`content` is blob
      startposition:
        line: 11
        column: 0
      endposition: null
- statement: |
    CREATE TABLE user(
      id INT PRIMARY KEY COMMENT 'comment',
      name VA<PERSON>HA<PERSON>(255) NOT NULL DEFAULT '' COMMENT 'comment',
      room_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      creator_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      created_ts TIMESTAMP NOT NULL DEFAULT NOW() COMMENT 'comment',
      updater_id INT NOT NULL DEFAULT 0 COMMENT 'comment',
      updated_ts TIMESTAMP NOT NULL DEFAULT NOW() ON UPDATE NOW() COMMENT 'comment',
      INDEX idx_user_name(name),
      UNIQUE KEY uk_user_id_name(id, name)
    ) ENGINE = INNODB COMMENT 'comment';
  changeType: 1
- statement: CREATE TABLE t(b BLOB, PRIMARY KEY(b(10)));
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(b BLOB, mb MEDIUMBLOB, lb LONGBLOB, id INT, PRIMARY KEY(b(1), mb(2), lb(3), id));
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(b BLOB, mb MEDIUMBLOB, lb LONGBLOB, id INT, UNIQUE INDEX(b(1), mb(2), lb(3), id))
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(b BLOB, mb MEDIUMBLOB, lb LONGBLOB, id iNT, INDEX(b(1), mb(2), lb(3), id))
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(id INT, PRIMARY KEY(id))
  changeType: 1
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN b BLOB, ADD COLUMN id INT, ADD PRIMARY KEY(b(1), id);
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN b BLOB, ADD COLUMN mb MEDIUMBLOB, ADD COLUMN lb LONGBLOB, ADD COLUMN id INT, ADD UNIQUE INDEX(b(1), mb(2), lb(3), id)
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN b BLOB, ADD COLUMN mb MEDIUMBLOB, ADD COLUMN lb LONGBLOB, ADD COLUMN id INT, ADD INDEX(b(1), mb(2), lb(3), id)
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(a int);
    ALTER TABLE t ADD COLUMN b BLOB, ADD COLUMN mb MEDIUMBLOB, ADD COLUMN lb LONGBLOB, ADD COLUMN id INT, ADD INDEX(id);
  changeType: 1
- statement: |-
    CREATE TABLE t(b blob, mb mediumblob, lb longblob, id int);
    CREATE INDEX idx_b ON t(b(5));
    CREATE INDEX idx_mb ON t(mb(5));
    CREATE INDEX idx_lb ON t(lb(5));
    CREATE INDEX idx_id ON t(id);
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`mb` is mediumblob
      startposition:
        line: 2
        column: 0
      endposition: null
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`lb` is longblob
      startposition:
        line: 3
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b blob, mb mediumblob, lb longblob, id int);
    CREATE INDEX idx_id ON t(id);
  changeType: 1
- statement: |-
    CREATE TABLE t(b int, mb mediumblob, lb longblob, id int);
    ALTER TABLE t MODIFY COLUMN b blob;
    ALTER TABLE t ADD PRIMARY KEY(b);
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`b` is blob
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(b int, mb mediumblob, lb longblob, id int);
    ALTER TABLE t CHANGE COLUMN b d blob;
    ALTER TABLE t ADD PRIMARY KEY(d);
  changeType: 1
  want:
    - status: 2
      code: 804
      title: index.type-no-blob
      content: Columns in index must not be BLOB but `t`.`d` is blob
      startposition:
        line: 2
        column: 0
      endposition: null
