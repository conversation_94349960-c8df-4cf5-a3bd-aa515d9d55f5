- statement: CREATE TABLE t(a int);
  changeType: 1
- statement: CREATE TABLE t(a JSO<PERSON>, b BLOB, c TEXT);
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column `t`.`a` is
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(d int);
    ALTER TABLE t add COLUMN (a JSON, b BLOB, c TEXT);
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column `t`.`a` is
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(d int);
    ALTER TABLE t ADD COLUMN (a JSON, b JSON);
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column `t`.`a` is
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSO<PERSON> but column `t`.`b` is
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(c int, b int);
    ALTER TABLE t CHANGE COLUMN c a JSON, MODIFY b BLOB;
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column `t`.`a` is
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(c int, b int);
    ALTER TABLE t MODIFY COLUMN c JSON;
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column `t`.`c` is
      startposition:
        line: 1
        column: 0
      endposition: null
