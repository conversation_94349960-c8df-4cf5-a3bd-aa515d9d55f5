package mysql

// Framework code is generated by the generator.

import (
	"context"
	"fmt"
	"strings"

	"github.com/antlr4-go/antlr/v4"
	"github.com/pkg/errors"

	mysql "github.com/bytebase/mysql-parser"

	"github.com/bytebase/bytebase/backend/common"
	"github.com/bytebase/bytebase/backend/plugin/advisor"
	"github.com/bytebase/bytebase/backend/plugin/advisor/catalog"
	mysqlparser "github.com/bytebase/bytebase/backend/plugin/parser/mysql"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

var (
	_ advisor.Advisor = (*IndexPkTypeAdvisor)(nil)
)

func init() {
	advisor.Register(storepb.Engine_MYSQL, advisor.MySQLIndexPKType, &IndexPkTypeAdvisor{})
	advisor.Register(storepb.Engine_MARIADB, advisor.MySQLIndexPKType, &IndexPkTypeAdvisor{})
	advisor.Register(storepb.Engine_OCEANBASE, advisor.MySQLIndexPKType, &IndexPkTypeAdvisor{})
}

// IndexPkTypeAdvisor is the advisor checking for correct type of PK.
type IndexPkTypeAdvisor struct {
}

// Check checks for correct type of PK.
func (*IndexPkTypeAdvisor) Check(_ context.Context, checkCtx advisor.Context) ([]*storepb.Advice, error) {
	stmtList, ok := checkCtx.AST.([]*mysqlparser.ParseResult)
	if !ok {
		return nil, errors.Errorf("failed to convert to mysql parse result")
	}

	level, err := advisor.NewStatusBySQLReviewRuleLevel(checkCtx.Rule.Level)
	if err != nil {
		return nil, err
	}
	checker := &indexPkTypeChecker{
		level:            level,
		title:            string(checkCtx.Rule.Type),
		line:             make(map[string]int),
		catalog:          checkCtx.Catalog,
		tablesNewColumns: make(tableColumnTypes),
	}

	for _, stmt := range stmtList {
		checker.baseLine = stmt.BaseLine
		antlr.ParseTreeWalkerDefault.Walk(checker, stmt.Tree)
	}

	return checker.adviceList, nil
}

type indexPkTypeChecker struct {
	*mysql.BaseMySQLParserListener

	baseLine         int
	adviceList       []*storepb.Advice
	level            storepb.Advice_Status
	title            string
	line             map[string]int
	catalog          *catalog.Finder
	tablesNewColumns tableColumnTypes
}

func (checker *indexPkTypeChecker) EnterCreateTable(ctx *mysql.CreateTableContext) {
	if !mysqlparser.IsTopMySQLRule(&ctx.BaseParserRuleContext) {
		return
	}
	if ctx.TableName() == nil {
		return
	}
	if ctx.TableElementList() == nil {
		return
	}

	_, tableName := mysqlparser.NormalizeMySQLTableName(ctx.TableName())
	for _, tableElement := range ctx.TableElementList().AllTableElement() {
		if tableElement == nil {
			continue
		}
		switch {
		case tableElement.ColumnDefinition() != nil:
			if tableElement.ColumnDefinition().FieldDefinition() == nil {
				continue
			}
			_, _, columnName := mysqlparser.NormalizeMySQLColumnName(tableElement.ColumnDefinition().ColumnName())
			checker.checkFieldDefinition(tableName, columnName, tableElement.ColumnDefinition().FieldDefinition())
		case tableElement.TableConstraintDef() != nil:
			checker.checkConstraintDef(tableName, tableElement.TableConstraintDef())
		}
	}
}

func (checker *indexPkTypeChecker) EnterAlterTable(ctx *mysql.AlterTableContext) {
	if !mysqlparser.IsTopMySQLRule(&ctx.BaseParserRuleContext) {
		return
	}
	if ctx.AlterTableActions() == nil {
		return
	}
	if ctx.AlterTableActions().AlterCommandList() == nil {
		return
	}
	if ctx.AlterTableActions().AlterCommandList().AlterList() == nil {
		return
	}
	if ctx.TableRef() == nil {
		return
	}

	_, tableName := mysqlparser.NormalizeMySQLTableRef(ctx.TableRef())
	for _, alterListItem := range ctx.AlterTableActions().AlterCommandList().AlterList().AllAlterListItem() {
		if alterListItem == nil {
			continue
		}

		switch {
		// add column
		case alterListItem.ADD_SYMBOL() != nil && alterListItem.Identifier() != nil:
			switch {
			case alterListItem.Identifier() != nil && alterListItem.FieldDefinition() != nil:
				columnName := mysqlparser.NormalizeMySQLIdentifier(alterListItem.Identifier())
				checker.checkFieldDefinition(tableName, columnName, alterListItem.FieldDefinition())
			case alterListItem.OPEN_PAR_SYMBOL() != nil && alterListItem.TableElementList() != nil:
				for _, tableElement := range alterListItem.TableElementList().AllTableElement() {
					if tableElement.ColumnDefinition() == nil || tableElement.ColumnDefinition().ColumnName() == nil || tableElement.ColumnDefinition().FieldDefinition() == nil {
						continue
					}
					_, _, columnName := mysqlparser.NormalizeMySQLColumnName(tableElement.ColumnDefinition().ColumnName())
					checker.checkFieldDefinition(tableName, columnName, tableElement.ColumnDefinition().FieldDefinition())
				}
			}
		// modify column
		case alterListItem.MODIFY_SYMBOL() != nil && alterListItem.ColumnInternalRef() != nil:
			columnName := mysqlparser.NormalizeMySQLColumnInternalRef(alterListItem.ColumnInternalRef())
			checker.checkFieldDefinition(tableName, columnName, alterListItem.FieldDefinition())
		// change column
		case alterListItem.CHANGE_SYMBOL() != nil && alterListItem.ColumnInternalRef() != nil && alterListItem.Identifier() != nil:
			oldColumnName := mysqlparser.NormalizeMySQLColumnInternalRef(alterListItem.ColumnInternalRef())
			checker.tablesNewColumns.delete(tableName, oldColumnName)
			newColumnName := mysqlparser.NormalizeMySQLIdentifier(alterListItem.Identifier())
			checker.checkFieldDefinition(tableName, newColumnName, alterListItem.FieldDefinition())
		// add constriant.
		case alterListItem.ADD_SYMBOL() != nil && alterListItem.TableConstraintDef() != nil:
			checker.checkConstraintDef(tableName, alterListItem.TableConstraintDef())
		}
	}
}

func (checker *indexPkTypeChecker) checkFieldDefinition(tableName, columnName string, ctx mysql.IFieldDefinitionContext) {
	if ctx.DataType() == nil {
		return
	}
	columnType := checker.getIntOrBigIntStr(ctx.DataType())
	for _, attribute := range ctx.AllColumnAttribute() {
		if attribute.PRIMARY_SYMBOL() != nil {
			checker.addAdvice(tableName, columnName, columnType, checker.baseLine+ctx.GetStart().GetLine())
		}
	}
	checker.tablesNewColumns.set(tableName, columnName, columnType)
}

func (checker *indexPkTypeChecker) checkConstraintDef(tableName string, ctx mysql.ITableConstraintDefContext) {
	if ctx.GetType_().GetTokenType() != mysql.MySQLParserPRIMARY_SYMBOL {
		return
	}
	if ctx.KeyListVariants() == nil {
		return
	}
	columnList := mysqlparser.NormalizeKeyListVariants(ctx.KeyListVariants())

	for _, columnName := range columnList {
		columnType, err := checker.getPKColumnType(tableName, columnName)
		if err != nil {
			continue
		}
		checker.addAdvice(tableName, columnName, columnType, checker.baseLine+ctx.GetStart().GetLine())
	}
}

func (checker *indexPkTypeChecker) addAdvice(tableName, columnName, columnType string, lineNumber int) {
	if !strings.EqualFold(columnType, "INT") && !strings.EqualFold(columnType, "BIGINT") {
		checker.adviceList = append(checker.adviceList, &storepb.Advice{
			Status:        checker.level,
			Code:          advisor.IndexPKType.Int32(),
			Title:         checker.title,
			Content:       fmt.Sprintf("Columns in primary key must be INT/BIGINT but `%s`.`%s` is %s", tableName, columnName, columnType),
			StartPosition: common.ConvertANTLRLineToPosition(lineNumber),
		})
	}
}

// getPKColumnType gets the column type string from v.tablesNewColumns or catalog, returns empty string and non-nil error if cannot find the column in given table.
func (checker *indexPkTypeChecker) getPKColumnType(tableName string, columnName string) (string, error) {
	if columnType, ok := checker.tablesNewColumns.get(tableName, columnName); ok {
		return columnType, nil
	}
	column := checker.catalog.Origin.FindColumn(&catalog.ColumnFind{
		TableName:  tableName,
		ColumnName: columnName,
	})
	if column != nil {
		return column.Type(), nil
	}
	return "", errors.Errorf("cannot find the type of `%s`.`%s`", tableName, columnName)
}

// getIntOrBigIntStr returns the type string of tp.
func (*indexPkTypeChecker) getIntOrBigIntStr(ctx mysql.IDataTypeContext) string {
	switch ctx.GetType_().GetTokenType() {
	// https://pkg.go.dev/github.com/pingcap/tidb/pkg/parser/mysql#TypeLong
	case mysql.MySQLParserINT_SYMBOL:
		// tp.String() return int(11)
		return "INT"
		// https://pkg.go.dev/github.com/pingcap/tidb/pkg/parser/mysql#TypeLonglong
	case mysql.MySQLParserBIGINT_SYMBOL:
		// tp.String() return bigint(20)
		return "BIGINT"
	}
	return strings.ToLower(ctx.GetParser().GetTokenStream().GetTextFromRuleContext(ctx))
}
