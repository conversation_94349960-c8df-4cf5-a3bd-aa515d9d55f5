- statement: CREATE TABLE t(a int, b int);
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "integer"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "integer"
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(a int, "B" int);
    CREATE INDEX ON t(a, "B");
    CREATE UNIQUE INDEX ON t(a, "B");
    DROP INDEX IF EXISTS t_a_B_idx_11111;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "integer"
                },
                {
                  "name": "B",
                  "position": 2,
                  "nullable": true,
                  "type": "integer"
                }
              ],
              "indexes": [
                {
                  "name": "t_a_B_idx",
                  "expressions": [
                    "a",
                    "B"
                  ],
                  "type": "btree"
                },
                {
                  "name": "t_a_B_idx1",
                  "expressions": [
                    "a",
                    "B"
                  ],
                  "type": "btree",
                  "unique": true
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    )
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "integer"
                },
                {
                  "name": "b",
                  "position": 2,
                  "default": "1",
                  "type": "integer"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "integer"
                }
              ],
              "indexes": [
                {
                  "name": "t_a_b_c_idx",
                  "expressions": [
                    "a",
                    "b",
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_c_idx",
                  "expressions": [
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_pkey",
                  "expressions": [
                    "a"
                  ],
                  "type": "btree",
                  "unique": true,
                  "primary": true
                },
                {
                  "name": "uk_a_b",
                  "expressions": [
                    "a",
                    "b"
                  ],
                  "type": "btree",
                  "unique": true
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    );
    ALTER TABLE t rename to t1;
    ALTER TABLE t1 rename column a to a1;
    alter table t1 rename constraint uk_a_b to ukk_a_b;
    alter table t1 add column d int;
    alter table t1 drop column c;
    alter table t1 alter column b set data type bigint;
    alter table t1 alter column a1 set default 1;
    alter table t1 alter column b drop default;
    alter table t1 alter column d set not null;
    alter table t1 add constraint uk_d unique (d);
    alter table t1 add constraint ukk_d unique (d);
    alter table t1 drop constraint ukk_d;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a1",
                  "position": 1,
                  "default": "1",
                  "type": "integer"
                },
                {
                  "name": "b",
                  "position": 2,
                  "type": "bigint"
                },
                {
                  "name": "d",
                  "position": 4,
                  "type": "integer"
                }
              ],
              "indexes": [
                {
                  "name": "t_a_b_c_idx",
                  "expressions": [
                    "a1",
                    "b",
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_c_idx",
                  "expressions": [
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_pkey",
                  "expressions": [
                    "a1"
                  ],
                  "type": "btree",
                  "unique": true,
                  "primary": true
                },
                {
                  "name": "uk_d",
                  "expressions": [
                    "d"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "ukk_a_b",
                  "expressions": [
                    "a1",
                    "b"
                  ],
                  "type": "btree",
                  "unique": true
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    );
    alter index uk_a_b rename to ukk_a_b;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "integer"
                },
                {
                  "name": "b",
                  "position": 2,
                  "default": "1",
                  "type": "integer"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "integer"
                }
              ],
              "indexes": [
                {
                  "name": "t_a_b_c_idx",
                  "expressions": [
                    "a",
                    "b",
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_c_idx",
                  "expressions": [
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_pkey",
                  "expressions": [
                    "a"
                  ],
                  "type": "btree",
                  "unique": true,
                  "primary": true
                },
                {
                  "name": "ukk_a_b",
                  "expressions": [
                    "a",
                    "b"
                  ],
                  "type": "btree",
                  "unique": true
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    );
    drop index uk_a_b;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "integer"
                },
                {
                  "name": "b",
                  "position": 2,
                  "default": "1",
                  "type": "integer"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "integer"
                }
              ],
              "indexes": [
                {
                  "name": "t_a_b_c_idx",
                  "expressions": [
                    "a",
                    "b",
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_c_idx",
                  "expressions": [
                    "c"
                  ],
                  "type": "btree",
                  "unique": true
                },
                {
                  "name": "t_pkey",
                  "expressions": [
                    "a"
                  ],
                  "type": "btree",
                  "unique": true,
                  "primary": true
                }
              ]
            },
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    );
    drop table t;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE public.t(
      a int primary key,
      b int NOT NULL DEFAULT 1,
      c int UNIQUE,
      UNIQUE (a, b, c),
      CONSTRAINT uk_a_b UNIQUE (a, b)
    );
    drop schema public;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres"
    }
  err: null
- statement: ALTER TABLE test DROP COLUMN id;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 408
    content: 'Cannot drop column "id" in table "public"."test", it''s referenced by view: "public"."v1"'
    line: 0
    payload:
      - '"public"."v1"'
- statement: ALTER TABLE test ALTER COLUMN id TYPE varchar(20);
  ignore_case_sensitive: false
  want: ""
  err:
    type: 408
    content: 'Cannot alter type of column "id" in table "public"."test", it''s referenced by view: "public"."v1"'
    line: 0
    payload:
      - '"public"."v1"'
- statement: DROP TABLE test;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 304
    content: 'Cannot drop table "public"."test", it''s referenced by view: "public"."v1"'
    line: 0
    payload:
      - '"public"."v1"'
- statement: |-
    DROP VIEW v1;
    ALTER TABLE test DROP COLUMN id;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "test",
              "columns": [
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    create view v1 as select * from t;
    alter view v1 rename to this_is_view_del;
    drop view this_is_view_del;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "postgres",
      "schemas": [
        {
          "name": "public",
          "tables": [
            {
              "name": "test",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar(20)"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
