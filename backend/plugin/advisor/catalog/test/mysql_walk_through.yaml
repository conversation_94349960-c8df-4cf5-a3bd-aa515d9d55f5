- statement: |-
    CREATE TABLE `test`.`t1` (a INT);
    CREATE TABLE `other`.`t1` (a INT);
    RENAME TABLE `other`.`t1` TO `other`.`t2`;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 201
    content: Database `other` is not the current database `test`
    line: 2
    payload: null
- statement: |-
    CREATE TABLE `test`.`t1` (a INT);
    RENAME TABLE `test`.`t1` TO `other`.`t1`;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {}
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(a INT);
    CREATE TABLE t2(a INT);
    RENAME TABLE t2 TO t1;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 301
    content: Table `t1` already exists
    line: 3
    payload: null
- statement: |-
    CREATE TABLE t1(a INT);
    RENAME TABLE t2 TO t3;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 302
    content: Table `t2` does not exist
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1 (a INT);
    RENAME TABLE t1 TO t2;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t2",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: CREATE DATABASE another;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 201
    content: Database `another` is not the current database `test`
    line: 1
    payload: null
- statement: |-
    -- this is comment
    DROP DATABASE test;
    CREATE TABLE t(a int);
  ignore_case_sensitive: false
  want: ""
  err:
    type: 202
    content: Database `test` is deleted
    line: 1
    payload: null
- statement: |-
    ALTER DATABASE CHARACTER SET = utf8mb4;
    ALTER DATABASE test COLLATE utf8mb4_polish_ci;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {}
      ],
      "characterSet": "utf8mb4",
      "collation": "utf8mb4_polish_ci"
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT PRIMARY KEY,
      c VARCHAR(255) NOT NULL
    );
    ALTER TABLE t1 ADD INDEX idx1 (c);
    CREATE FULLTEXT INDEX ftk1 ON t1 (c);
    DROP INDEX `PRIMARY` ON t1;
    DROP INDEX idx1 ON t1;
    DROP INDEX ftk1 ON t1;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "c",
                  "position": 2,
                  "type": "varchar"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT,
      b INT,
      c VARCHAR(255) NOT NULL,
      d GEOMETRY NOT NULL
    );
    CREATE INDEX idx1 ON t1 (a) INVISIBLE;
    CREATE UNIQUE INDEX uk1 ON t1 (b);
    CREATE FULLTEXT INDEX ftk1 ON t1 (c);
    CREATE SPATIAL INDEX sk1 ON t1 (d);
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "c",
                  "position": 3,
                  "type": "varchar"
                },
                {
                  "name": "d",
                  "position": 4,
                  "type": "geometry"
                }
              ],
              "indexes": [
                {
                  "name": "ftk1",
                  "expressions": [
                    "c"
                  ],
                  "type": "FULLTEXT",
                  "visible": true
                },
                {
                  "name": "idx1",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE"
                },
                {
                  "name": "sk1",
                  "expressions": [
                    "d"
                  ],
                  "type": "SPATIAL",
                  "visible": true
                },
                {
                  "name": "uk1",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(a GEOMETRY);
    CREATE SPATIAL INDEX sk1 ON t1 (a);
  ignore_case_sensitive: false
  want: ""
  err:
    type: 507
    content: All parts of a SPATIAL index must be NOT NULL, but `a` is nullable
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(
      a INT PRIMARY KEY,
      b INT,
      UNIQUE uk1 (b)
    );
    ALTER TABLE t1 ENGINE = MyISAM;
    ALTER TABLE t1 COMMENT = 't1 table';
    ALTER TABLE t1 COLLATE utf8mb4_unicode_ci;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "int"
                }
              ],
              "indexes": [
                {
                  "name": "PRIMARY",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "primary": true,
                  "visible": true
                },
                {
                  "name": "uk1",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                }
              ],
              "engine": "MyISAM",
              "collation": "utf8mb4_unicode_ci",
              "comment": "t1 table"
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT PRIMARY KEY,
      b INT,
      UNIQUE uk1 (b)
    );
    ALTER TABLE t1 DROP PRIMARY KEY;
    ALTER TABLE t1 DROP INDEX uk1;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT,
      b INT,
      UNIQUE uk1 (b)
    );
    ALTER TABLE t1 RENAME INDEX uk1 TO uk_b;
    ALTER TABLE t1 ADD INDEX idx1 (b);
    ALTER TABLE t1 RENAME INDEX idx1 TO idx2;
    ALTER TABLE t1 ALTER INDEX idx2 INVISIBLE;
    ALTER TABLE t1 RENAME TO t2;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t2",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "int"
                }
              ],
              "indexes": [
                {
                  "name": "idx2",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE"
                },
                {
                  "name": "uk_b",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(a INT NOT NULL);
    ALTER TABLE t1 ALTER COLUMN a SET DEFAULT NULL;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 406
    content: Invalid default value for column `a`
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(a INT);
    ALTER TABLE t1 CHANGE COLUMN a a BLOB;
    ALTER TABLE t1 ALTER COLUMN a SET DEFAULT 'default value';
  ignore_case_sensitive: false
  want: ""
  err:
    type: 407
    content: BLOB, TEXT, GEOMETRY or JSON column `a` can't have a default value
    line: 3
    payload: null
- statement: |-
    CREATE TABLE t1(a INT);
    ALTER TABLE t1 CHANGE COLUMN a a BLOB;
    ALTER TABLE t1 ALTER COLUMN a SET DEFAULT NULL;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "blob"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT
    );
    ALTER TABLE t1 ADD COLUMN b INT AFTER a;
    ALTER TABLE t1 ADD COLUMN c INT NOT NULL DEFAULT 1 AFTER a;
    ALTER TABLE t1 CHANGE COLUMN b d TEXT AFTER a;
    ALTER TABLE t1 ALTER COLUMN a SET DEFAULT 1;
    ALTER TABLE t1 ALTER COLUMN a SET INVISIBLE;
    ALTER TABLE t1 ALTER COLUMN c DROP DEFAULT;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "default": "1",
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "d",
                  "position": 2,
                  "nullable": true,
                  "type": "text"
                },
                {
                  "name": "c",
                  "position": 3,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT
    );
    ALTER TABLE t1 ADD COLUMN b VARCHAR(255) FIRST;
    ALTER TABLE t1 ADD COLUMN (c INT, d BLOB);
    ALTER TABLE t1 MODIFY COLUMN b INT AFTER a;
    ALTER TABLE t1 RENAME COLUMN b TO e;
    ALTER TABLE t1 CHANGE COLUMN d f TEXT AFTER e;
    ALTER TABLE t1 DROP COLUMN a;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "e",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "f",
                  "position": 2,
                  "nullable": true,
                  "type": "text"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(a INT);
    CREATE TABLE t2(a INT);
    DROP TABLE t1, t2;
    CREATE TABLE t3(a INT);
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t3",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT AUTO_INCREMENT,
      b INT AUTO_INCREMENT
    );
  ignore_case_sensitive: false
  want: ""
  err:
    type: 404
    content: There can be only one auto column for table `t1`
    line: 1
    payload: null
- statement: CREATE TABLE tvx SELECT (x,z) FROM (VALUES ROW(1,3,5), ROW(2,4,6)) AS v(x,y,z);
  ignore_case_sensitive: false
  want: ""
  err:
    type: 303
    content: Disallow the CREATE TABLE AS statement but "CREATE TABLE tvx SELECT (x,z) FROM (VALUES ROW(1,3,5), ROW(2,4,6)) AS v(x,y,z);" uses
    line: 1
    payload: null
- statement: |-
    CREATE TABLE t1(a int);
    CREATE TABLE t2 (b INT) SELECT a FROM t1;
  ignore_case_sensitive: false
  want: ""
  err:
    type: 303
    content: Disallow the CREATE TABLE AS statement but "CREATE TABLE t2 (b INT) SELECT a FROM t1;" uses
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(
      a int
    );
    CREATE TABLE t2 LIKE t1;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                }
              ]
            },
            {
              "name": "t2",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT PRIMARY KEY,
      b INT NOT NULL DEFAULT 1,
      c INT,
      d VARCHAR(255) NOT NULL,
      e VARCHAR(255),
      UNIQUE KEY uk1 (b, c),
      KEY idx1 (c),
      FULLTEXT(d, e) WITH PARSER ngram INVISIBLE
    );
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "default": "1",
                  "type": "int"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "d",
                  "position": 4,
                  "type": "varchar"
                },
                {
                  "name": "e",
                  "position": 5,
                  "nullable": true,
                  "type": "varchar"
                }
              ],
              "indexes": [
                {
                  "name": "PRIMARY",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "primary": true,
                  "visible": true
                },
                {
                  "name": "d",
                  "expressions": [
                    "d",
                    "e"
                  ],
                  "type": "FULLTEXT"
                },
                {
                  "name": "idx1",
                  "expressions": [
                    "c"
                  ],
                  "type": "BTREE",
                  "visible": true
                },
                {
                  "name": "uk1",
                  "expressions": [
                    "b",
                    "c"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      a INT NOT NULL DEFAULT NULL
    );
  ignore_case_sensitive: false
  want: ""
  err:
    type: 406
    content: Invalid default value for column `a`
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(
      a BLOB DEFAULT 'this is a default value'
    );
  ignore_case_sensitive: false
  want: ""
  err:
    type: 407
    content: BLOB, TEXT, GEOMETRY or JSON column `a` can't have a default value
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(
      a INT ON UPDATE NOW()
    );
  ignore_case_sensitive: false
  want: ""
  err:
    type: 405
    content: Column `a` use ON UPDATE but is not DATETIME or TIMESTAMP
    line: 2
    payload: null
- statement: |-
    CREATE TABLE t1(
      a INT NOT NULL DEFAULT 1 PRIMARY KEY,
      b INT UNIQUE,
      c VARCHAR(255) NOT NULL DEFAULT 'NULL' COMMENT 'column c',
      e INT NULL DEFAULT NULL,
      f VARCHAR(10) NOT NULL COLLATE utf8mb4_polish_ci,
      g VARCHAR(200) CHARACTER SET utf8mb4 NOT NULL
    );
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "default": "1",
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "c",
                  "position": 3,
                  "default": "NULL",
                  "type": "varchar",
                  "comment": "column c"
                },
                {
                  "name": "e",
                  "position": 4,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "f",
                  "position": 5,
                  "type": "varchar",
                  "collation": "utf8mb4_polish_ci"
                },
                {
                  "name": "g",
                  "position": 6,
                  "type": "varchar",
                  "characterSet": "utf8mb4"
                }
              ],
              "indexes": [
                {
                  "name": "PRIMARY",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "primary": true,
                  "visible": true
                },
                {
                  "name": "b",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t1(
      id int,
      name varchar(255),
      key idx_name(name)
    );
    create table t2 (
      id int,
      t1_name varchar(255),
      constraint fk_t1_name foreign key (t1_name) references t1 (name)
    );
    alter table t2 drop foreign key fk_t1_name;
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "t1",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar"
                }
              ],
              "indexes": [
                {
                  "name": "idx_name",
                  "expressions": [
                    "name"
                  ],
                  "type": "BTREE",
                  "visible": true
                }
              ]
            },
            {
              "name": "t2",
              "columns": [
                {
                  "name": "id",
                  "position": 1,
                  "nullable": true,
                  "type": "int"
                },
                {
                  "name": "t1_name",
                  "position": 2,
                  "nullable": true,
                  "type": "varchar"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    create table all_types(
      c1 tinyblob not null,
      c2 blob not null,
      c3 mediumblob not null,
      c4 longblob not null,
      c5 tinytext not null,
      c6 text not null,
      c7 mediumtext not null,
      c8 longtext not null,
      c9 json not null,
      c10 serial not null,
      c11 long varbinary not null,
      c12 long varchar not null 
    );
  ignore_case_sensitive: false
  want: |-
    {
      "name": "test",
      "schemas": [
        {
          "tables": [
            {
              "name": "all_types",
              "columns": [
                {
                  "name": "c1",
                  "position": 1,
                  "type": "tinyblob"
                },
                {
                  "name": "c2",
                  "position": 2,
                  "type": "blob"
                },
                {
                  "name": "c3",
                  "position": 3,
                  "type": "mediumblob"
                },
                {
                  "name": "c4",
                  "position": 4,
                  "type": "longblob"
                },
                {
                  "name": "c5",
                  "position": 5,
                  "type": "tinytext"
                },
                {
                  "name": "c6",
                  "position": 6,
                  "type": "text"
                },
                {
                  "name": "c7",
                  "position": 7,
                  "type": "mediumtext"
                },
                {
                  "name": "c8",
                  "position": 8,
                  "type": "longtext"
                },
                {
                  "name": "c9",
                  "position": 9,
                  "type": "json"
                },
                {
                  "name": "c10",
                  "position": 10,
                  "type": "serial"
                },
                {
                  "name": "c11",
                  "position": 11,
                  "type": "long"
                },
                {
                  "name": "c12",
                  "position": 12,
                  "type": "long"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
