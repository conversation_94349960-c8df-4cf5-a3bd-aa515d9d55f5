- statement: CREATE INDEX idx_a on t(a)
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {
          "tables": [
            {
              "name": "t",
              "indexes": [
                {
                  "name": "idx_a",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: ALTER TABLE t RENAME COLUMN a TO a_copy
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a_copy"
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: ALTER TABLE t RENAME TO t1
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {
          "tables": [
            {
              "name": "t1"
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    ALTER TABLE t ADD PRIMARY KEY (a);
    ALTER TABLE t ADD UNIQUE (b);
    CREATE INDEX idx_a on t(a);
    CREATE INDEX b_2 on t(b, a);
    CREATE UNIQUE INDEX b_3 on t(b, c, d);
    CREATE FULLTEXT INDEX b_4 on t(b, d) WITH PARSER ngram INVISIBLE;
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {
          "tables": [
            {
              "name": "t",
              "indexes": [
                {
                  "name": "PRIMARY",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "primary": true,
                  "visible": true
                },
                {
                  "name": "b",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                },
                {
                  "name": "b_2",
                  "expressions": [
                    "b",
                    "a"
                  ],
                  "type": "BTREE",
                  "visible": true
                },
                {
                  "name": "b_3",
                  "expressions": [
                    "b",
                    "c",
                    "d"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                },
                {
                  "name": "b_4",
                  "expressions": [
                    "b",
                    "d"
                  ],
                  "type": "FULLTEXT"
                },
                {
                  "name": "idx_a",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: |-
    CREATE TABLE t(
      a int PRIMARY KEY DEFAULT 1,
      b varchar(200) CHARACTER SET utf8mb4 NOT NULL UNIQUE,
      c int auto_increment NULL COMMENT 'This is a comment',
      d varchar(10) COLLATE utf8mb4_polish_ci,
      KEY idx_a (a),
      INDEX (b, a),
      UNIQUE (b, c, d),
      FULLTEXT (b, d) WITH PARSER ngram INVISIBLE
    )
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {
          "tables": [
            {
              "name": "t",
              "columns": [
                {
                  "name": "a",
                  "position": 1,
                  "default": "1",
                  "type": "int"
                },
                {
                  "name": "b",
                  "position": 2,
                  "type": "varchar",
                  "characterSet": "utf8mb4"
                },
                {
                  "name": "c",
                  "position": 3,
                  "nullable": true,
                  "type": "int",
                  "comment": "This is a comment"
                },
                {
                  "name": "d",
                  "position": 4,
                  "nullable": true,
                  "type": "varchar",
                  "collation": "utf8mb4_polish_ci"
                }
              ],
              "indexes": [
                {
                  "name": "PRIMARY",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "primary": true,
                  "visible": true
                },
                {
                  "name": "b",
                  "expressions": [
                    "b"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                },
                {
                  "name": "b_2",
                  "expressions": [
                    "b",
                    "a"
                  ],
                  "type": "BTREE",
                  "visible": true
                },
                {
                  "name": "b_3",
                  "expressions": [
                    "b",
                    "c",
                    "d"
                  ],
                  "type": "BTREE",
                  "unique": true,
                  "visible": true
                },
                {
                  "name": "b_4",
                  "expressions": [
                    "b",
                    "d"
                  ],
                  "type": "FULLTEXT"
                },
                {
                  "name": "idx_a",
                  "expressions": [
                    "a"
                  ],
                  "type": "BTREE",
                  "visible": true
                }
              ]
            }
          ]
        }
      ]
    }
  err: null
- statement: DROP TABLE t1, t2
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {}
      ]
    }
  err: null
- statement: INSERT INTO test values (1)
  ignore_case_sensitive: false
  want: |-
    {
      "schemas": [
        {}
      ]
    }
  err: null
