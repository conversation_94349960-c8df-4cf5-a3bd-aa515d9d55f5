// Package oracle is the advisor for oracle database.
package oracle

import (
	"testing"

	"github.com/bytebase/bytebase/backend/plugin/advisor"
	storepb "github.com/bytebase/bytebase/proto/generated-go/store"
)

func TestOracleRules(t *testing.T) {
	oracleRules := []advisor.SQLReviewRuleType{
		advisor.SchemaRuleTableRequirePK,
		advisor.SchemaRuleTableNoFK,
		advisor.SchemaRuleTableNaming,
		advisor.SchemaRuleRequiredColumn,
		advisor.SchemaRuleColumnTypeDisallowList,
		advisor.SchemaRuleColumnMaximum<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
		advisor.SchemaRuleStatementNoSelectAll,
		advisor.SchemaRuleStatementNoLeadingWildcardLike,
		advisor.SchemaRuleStatementRequireWhereForSelect,
		advisor.SchemaRuleStatementRequireWhereForUpdateDele<PERSON>,
		advisor.SchemaRuleStatementInsertMustSpecifyColumn,
		advisor.SchemaRuleIndexKeyNumberLimit,
		advisor.SchemaRuleColumnNot<PERSON>ull,
		advisor.SchemaRuleColumnR<PERSON><PERSON><PERSON><PERSON><PERSON>,
		advisor.SchemaRuleAddNotNullColumnRequireDefault,
		advisor.SchemaRuleColumnMaximumVarcharLength,
		advisor.SchemaRuleTableNameNoKeyword,
		advisor.SchemaRuleIdentifierNoKeyword,
		advisor.SchemaRuleIdentifierCase,
		advisor.SchemaRuleStatementDisallowMixInDDL,
		advisor.SchemaRuleStatementDisallowMixInDML,
		advisor.SchemaRuleTableCommentConvention,
		advisor.SchemaRuleColumnCommentConvention,
	}

	for _, rule := range oracleRules {
		advisor.RunSQLReviewRuleTest(t, rule, storepb.Engine_ORACLE, false, false /* record */)
	}
}
