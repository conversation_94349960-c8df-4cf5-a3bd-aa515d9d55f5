- statement: |-
    CREATE TABLE book (
      id int,
      name varchar(255)
    )
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "ID" is nullable, which is not allowed.
      startposition:
        line: 1
        column: 0
      endposition: null
    - status: 2
      code: 402
      title: column.no-null
      content: Column "NAME" is nullable, which is not allowed.
      startposition:
        line: 2
        column: 0
      endposition: null
- statement: CREATE TABLE book(id int, name varchar(255), PRIMARY KEY (id))
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "NAME" is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE book(id int PRIMARY KEY, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "NAME" is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE book(id int NOT NULL, name var<PERSON><PERSON>(255))
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "NAME" is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE book(id int PRIMARY KEY, name varchar(255) NOT NULL)
  changeType: 1
- statement: ALTER TABLE tech_book ADD reader int
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "READER" is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    ALTER TABLE tech_book DROP COLUMN id;
    ALTER TABLE tech_book DROP COLUMN name;
    ALTER TABLE tech_book ADD id int PRIMARY KEY;
    ALTER TABLE tech_book ADD name varchar(255) NOT NULL;
  changeType: 1
- statement: ALTER TABLE tech_book MODIFY id NOT NULL
  changeType: 1
- statement: ALTER TABLE tech_book MODIFY name NULL
  changeType: 1
  want:
    - status: 2
      code: 402
      title: column.no-null
      content: Column "NAME" is nullable, which is not allowed.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: -- this is a comment
  changeType: 1
