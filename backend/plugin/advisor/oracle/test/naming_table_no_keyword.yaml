- statement: CREATE TABLE "table"(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.table.no-keyword
      content: Table name "table" is a keyword identifier and should be avoided.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE "rlcmidzlevbivwvcntihenpoibtsdfasdfasdfiutqeb"(id int, name varchar(255))
  changeType: 1
- statement: CREATE TABLE avg(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.table.no-keyword
      content: Table name "AVG" is a keyword identifier and should be avoided.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book RENAME TO avg
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.table.no-keyword
      content: Table name "AVG" is a keyword identifier and should be avoided.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    create table techBook(id int);
    ALTER TABLE techBook RENAME TO tech_book_tmp;
  changeType: 1
