- statement: CREATE TABLE "table"(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.identifier.no-keyword
      content: Identifier "NAME" is a keyword and should be avoided
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 308
      title: naming.identifier.no-keyword
      content: Identifier "table" is a keyword and should be avoided
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE "rlcmidzlevbivwvcntihenpoibtsdfasdfasdfiutqeb"(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.identifier.no-keyword
      content: Identifier "NAME" is a keyword and should be avoided
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE analyze(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 308
      title: naming.identifier.no-keyword
      content: Identifier "NAME" is a keyword and should be avoided
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book RENAME TO analyze
  changeType: 1
- statement: |-
    create table techBook(id int);
    ALTER TABLE techBook RENAME TO tech_book_tmp;
  changeType: 1
