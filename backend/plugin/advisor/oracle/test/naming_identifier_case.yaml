- statement: CREATE TABLE "table"(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 309
      title: naming.identifier.case
      content: Identifier "table" should be upper case
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE tt(id int, name varchar(255))
  changeType: 1
- statement: CREATE TABLE "Tt"(id int, name varchar(255))
  changeType: 1
  want:
    - status: 2
      code: 309
      title: naming.identifier.case
      content: Identifier "Tt" should be upper case
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book RENAME TO analyze
  changeType: 1
- statement: |-
    create table techBook(id int);
    ALTER TABLE techBook RENAME TO tech_book_tmp;
  changeType: 1
