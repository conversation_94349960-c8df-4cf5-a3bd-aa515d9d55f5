- statement: CREATE TABLE t(name char(20));
  changeType: 1
- statement: CREATE TABLE t(name varchar(225));
  changeType: 1
- statement: CREATE TABLE t(name char(225));
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The maximum character length is 20.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE public.tech_book ADD name_2 char(225)
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The maximum character length is 20.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name char(225)
  changeType: 1
  want:
    - status: 2
      code: 415
      title: column.maximum-character-length
      content: The maximum character length is 20.
      startposition:
        line: 0
        column: 0
      endposition: null
