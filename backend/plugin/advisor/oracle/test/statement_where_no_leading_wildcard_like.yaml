- statement: SELECT * FROM t WHERE a LIKE 'abc%'
  changeType: 1
- statement: SELECT * FROM t WHERE a LIKE '%abc'
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT * FROM t WHERE a LIKE 'abc' OR a LIKE '%abc'
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT * FROM t WHERE a LIKE '%acc' OR a LIKE '%abc'
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: SELECT * FROM (SELECT * FROM t WHERE a LIKE '%acc' OR a LIKE '%abc') t1
  changeType: 1
  want:
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
    - status: 2
      code: 204
      title: statement.where.no-leading-wildcard-like
      content: Avoid using leading wildcard LIKE.
      startposition:
        line: 0
        column: 0
      endposition: null
