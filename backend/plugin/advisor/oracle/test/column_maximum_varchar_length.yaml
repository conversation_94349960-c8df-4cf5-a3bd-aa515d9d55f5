- statement: CREATE TABLE t(name varchar(20));
  changeType: 1
- statement: CREATE TABLE t(name varchar(3000));
  changeType: 1
  want:
    - status: 2
      code: 422
      title: column.maximum-varchar-length
      content: The maximum varchar length is 2560.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: CREATE TABLE t(name varchar(225));
  changeType: 1
- statement: ALTER TABLE public.tech_book ADD name_2 varchar(22225)
  changeType: 1
  want:
    - status: 2
      code: 422
      title: column.maximum-varchar-length
      content: The maximum varchar length is 2560.
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: ALTER TABLE tech_book MODIFY name varchar(22225)
  changeType: 1
  want:
    - status: 2
      code: 422
      title: column.maximum-varchar-length
      content: The maximum varchar length is 2560.
      startposition:
        line: 0
        column: 0
      endposition: null
