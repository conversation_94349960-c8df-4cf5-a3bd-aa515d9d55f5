- statement: CREATE TABLE t(a char(5));
  changeType: 1
- statement: CREATE TABLE t(a int, b JSON);
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column "B" is
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(d char(5));
    ALTER TABLE t add a JSON;
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type JSON but column "A" is
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(d char(5));
    ALTER TABLE t MODIFY d BINARY_FLOAT;
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type BINARY_FLOAT but column "D" is
      startposition:
        line: 1
        column: 0
      endposition: null
- statement: CREATE TABLE t(a int, b BINARY_FLOAT);
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type BINARY_FLOAT but column "B" is
      startposition:
        line: 0
        column: 0
      endposition: null
- statement: |-
    CREATE TABLE t(d char(5));
    ALTER TABLE t add a BINARY_FLOAT;
  changeType: 1
  want:
    - status: 2
      code: 411
      title: column.type-disallow-list
      content: Disallow column type BINARY_FLOAT but column "A" is
      startposition:
        line: 1
        column: 0
      endposition: null
